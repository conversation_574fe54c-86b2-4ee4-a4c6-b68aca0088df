{"name": "gus-eip-integration-handlers", "version": "1.0.0", "description": "This project has been generated using the `aws-nodejs-typescript` template from the [Serverless framework](https://www.serverless.com/).", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "postinstall": ""}, "devDependencies": {"@serverless/typescript": "3.21.0", "@types/aws-lambda": "^8.10.149", "@types/node": "^22.13.5", "dotenv": "^17.2.0", "esbuild": "^0.21.4", "json-schema-to-ts": "^1.5.0", "serverless-esbuild": "^1.52.1", "ts-node": "^10.4.0", "tsconfig-paths": "^3.9.0", "typescript": "^4.1.3"}, "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-dynamodb": "^3.828.0", "@aws-sdk/client-secrets-manager": "^3.758.0", "@aws-sdk/client-sqs": "^3.772.0", "@aws-sdk/lib-dynamodb": "^3.828.0", "@gus-eip/loggers": "4.1.4", "@nestjs/common": "^10.3.7", "@types/he": "^1.2.3", "@types/ssh2": "^1.11.18", "@types/ssh2-sftp-client": "^9.0.1", "aws-sdk": "^2.1621.0", "axios": "^1.6.5", "cache-manager": "^5.6.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "he": "^1.2.0", "https": "^1.0.0", "i18n-iso-countries": "^7.14.0", "libphonenumber-js": "^1.12.8", "nanoid": "^5.1.5", "node-fetch": "^3.3.2", "path": "^0.12.7", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "ssh2": "^1.16.0", "ssh2-sftp-client": "^12.0.0", "uuid": "^11.1.0"}}