import { DynamoDBService } from "src/common/dynamodbService";
import { v4 as uuidv4 } from 'uuid';

const dbService = new DynamoDBService()

export async function storeFailedRecords(record: any, TableName: any, PK: any) {
    console.log('Failed record', JSON.stringify(record))
    const eventBody = JSON.parse(record.body)
    const eventMessage = JSON.parse(eventBody.Message)
    try {
        const checkExistingFailedRecordDataResponse = await checkExistingFailedRecordData(
            record,
            TableName,
            PK
        )
        console.log('CheckExistingFailedRecordDataResponse', checkExistingFailedRecordDataResponse)

        console.log("Message before modification -->", eventMessage);
        const sk = eventMessage.uuid || record?.messageId || uuidv4();
        eventMessage.uuid = sk;
        console.log("Message after adding uuid -->", eventMessage);
        eventMessage.Message = JSON.stringify(eventMessage);
        record.body = JSON.stringify(eventMessage);

        return await dbService.putObject(
            TableName,
            {
                PK: `${PK}#${record.attributes.MessageGroupId}`,
                SK: sk,
                ...record
            }
        )
    } catch (error) {
        throw new Error(error)
    }
}

export async function storeFailedRecordsByExternalApplicationId(record: any, TableName: any, PK: any) {
    console.log('Failed record by external application ID', JSON.stringify(record))
    const eventBody = JSON.parse(record.body)
    const eventMessage = JSON.parse(eventBody.Message)
    
    try {
        const externalApplicationId = eventMessage.payload?.applicationDetails?.externalApplicationId;
        if (!externalApplicationId) {
            throw new Error('External application ID not found in event message');
        }

        const checkExistingFailedRecordDataResponse = await checkExistingFailedRecordDataByExternalId(
            externalApplicationId,
            TableName,
            PK
        )
        console.log('CheckExistingFailedRecordDataResponse', checkExistingFailedRecordDataResponse)

        console.log("Message before modification -->", eventMessage);
        const sk = eventMessage.uuid || record?.messageId || uuidv4();
        eventMessage.uuid = sk;
        console.log("Message after adding uuid -->", eventMessage);
        eventMessage.Message = JSON.stringify(eventMessage);
        record.body = JSON.stringify(eventMessage);

        return await dbService.putObject(
            TableName,
            {
                PK: `${PK}#${externalApplicationId}`,
                SK: sk,
                ...record
            }
        )
    } catch (error) {
        throw new Error(error)
    }
}

export async function checkExistingFailedRecordData(record: any, TableName: any, PK: any) {
    try {
        const messageGroupId = record.attributes.MessageGroupId;
        const data = await dbService.getObject(
            TableName,
            {
                PK: PK,
                SK: messageGroupId
            }
        );

        if (data.Item) {
            return "Record already exists"
        } else {
            await dbService.putObject(
                TableName,
                {
                    PK: PK,
                    SK: messageGroupId,
                    status: "Failed"
                }
            )

            return 'Partition record created successfully'
        }
    } catch (err) {
        throw err
    }
}

export async function checkExistingFailedRecordDataByExternalId(externalApplicationId: any, TableName: any, PK: any) {
    try {
        const data = await dbService.getObject(
            TableName,
            {
                PK: PK,
                SK: externalApplicationId
            }
        );

        if (data.Item) {
            return "Record already exists"
        } else {
            await dbService.putObject(
                TableName,
                {
                    PK: PK,
                    SK: externalApplicationId,
                    status: "Failed"
                }
            )

            return 'Partition record created successfully'
        }
    } catch (err) {
        throw err
    }
}

export async function storeFailedRecordsQueue(applicationId: any, TableName: any, integrationService: any, record: any) {
    console.log('Failed record', record)
    try {
        await checkRecordFailedRecordDataQueue(
            applicationId,
            TableName,
            integrationService
        )

        const recordBody = JSON.parse(record.body);
        let message = JSON.parse(recordBody.Message);

        console.log("Message before modification -->", message);
        const sk = message.uuid || record?.messageId || uuidv4();
        message.uuid = sk;
        console.log("Message after adding uuid -->", message);
        recordBody.Message = JSON.stringify(message);
        record.body = JSON.stringify(recordBody);

        console.log("Updated record.body -->", record.body);

        return await dbService.putObject(
            TableName,
            {
                PK: `${integrationService}#${applicationId}`,
                SK: sk,
                ...record,
            }
        );
    } catch (error) {
        throw new Error(error)
    }
}

export async function checkRecordFailedRecordDataQueue(applicationId: any, TableName: any, PK: any) {
    try {
        const data = await dbService.getObject(
            TableName,
            {
                PK: PK,
                SK: applicationId
            }
        );

        if (data.Item) {
            return "Record already exists"
        } else {
            await dbService.putObject(
                TableName,
                {
                    PK: PK,
                    SK: applicationId,
                    status: "Failed"
                }
            )

            return 'Partition record created successfully'
        }
    } catch (error) {
        throw error
    }
}