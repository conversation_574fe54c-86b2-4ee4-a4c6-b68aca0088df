import { DynamoDBService } from "src/common/dynamodbService";
import {
  storeFailedRecords,
  storeFailedRecordsQueue,
  storeFailedRecordsByExternalApplicationId,
} from "./storeFailedRecords";
const dbService = new DynamoDBService();

//process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME
export async function checkExistingMessageGroupId(
  record: any,
  TableName: string,
  PK: string
): Promise<string | undefined> {
  try {
    const eventBody = JSON.parse(record.body);
    const platformEventMessage = JSON.parse(eventBody.Message);
    if (platformEventMessage.status) {
      return "No messages to process";
    } else {
      const messageGroupId = record.attributes.MessageGroupId;
      if (!messageGroupId) return "No messages to process";
      const data = await dbService.getObject(TableName, {
        PK: PK,
        SK: messageGroupId,
      });

      if (data.Item && (data.Item.status === 'Failed' || !data.Item.status)) {
        await storeFailedRecords(record, TableName, PK);
        return "A record with the same messageGroupId failed earlier or has no status, so this record can't proceed further."
      }
      return "No messages to process";
    }// Already processed
  } catch (error: any) {
    console.error("Error checking messageGroupId:", error);
    throw new Error(error?.message || "Unexpected error");
  }
}

export async function checkApplicationIdExist(
  request,
  applicationId: any,
  TableName: any,
  PK: any
) {
  try {
    const recordBody = JSON.parse(request.body);
    let message = JSON.parse(recordBody.Message);
    if (message.status) {
      return "No messages to process";
    } else {
      const data = await dbService.getObject(TableName, {
        PK: PK,
        SK: applicationId,
      });

      if (data.Item) {
        await storeFailedRecordsQueue(applicationId, TableName, PK, request);
        console.log(
          "A record with the same messageGroupId failed earlier, so this record can't proceed further"
        );
        return "A record with the same messageGroupId failed earlier, so this record can't proceed further";
      } else {
        return "No messages to process";
      }
    }
  } catch (error) {
    console.log("err", error);
    throw new Error(error);
  }
}

export async function getRetryCountIfExists(
  TableName: string,
  PK: string,
  SK: string
): Promise<number> {
  try {
    const data = await dbService.getObject(TableName, { PK, SK });

    const retryCount = data?.Item?.retryCount;

    // Ensure it's a valid number; otherwise default to 0
    if (typeof retryCount === "number" && !isNaN(retryCount)) {
      return retryCount;
    }

    return 0;
  } catch (error) {
    console.error("Error fetching retryCount:", error);
    return 0; // Default to 0 if there's an error
  }
}

export async function checkExistingByExternalApplicationId(
  record: any,
  TableName: string,
  PK: string
): Promise<string | undefined> {
  try {
    const eventBody = JSON.parse(record.body);
    const platformEventMessage = JSON.parse(eventBody.Message);
    if (platformEventMessage.status) {
      return "No messages to process";
    } else {
      const externalApplicationId = platformEventMessage.payload?.applicationDetails?.externalApplicationId;
      if (!externalApplicationId) return "No messages to process";
      
      const data = await dbService.getObject(TableName, {
        PK: PK,
        SK: externalApplicationId,
      });

      if (data.Item && (data.Item.status === 'Failed' || !data.Item.status)) {
        await storeFailedRecordsByExternalApplicationId(record, TableName, PK);
        return "A record with the same externalApplicationId failed earlier or has no status, so this record can't proceed further."
      }
      return "No messages to process";
    }
  } catch (error: any) {
    console.error("Error checking externalApplicationId:", error);
    throw new Error(error?.message || "Unexpected error");
  }
}

export async function getRetryCountByExternalApplicationId(
  TableName: string,
  PK: string,
  externalApplicationId: string
): Promise<number> {
  try {
    const data = await dbService.getObject(TableName, { PK, SK: externalApplicationId });

    const retryCount = data?.Item?.retryCount;

    // Ensure it's a valid number; otherwise default to 0
    if (typeof retryCount === "number" && !isNaN(retryCount)) {
      return retryCount;
    }

    return 0;
  } catch (error) {
    console.error("Error fetching retryCount by externalApplicationId:", error);
    return 0; // Default to 0 if there's an error
  }
}
