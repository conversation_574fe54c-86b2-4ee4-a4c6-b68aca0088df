/**
 * Utility functions for handling education history ordering and operations
 * Provides backward compatibility between Institution_Number__c and Name fields
 */

export interface EducationHistoryRecord {
  Id: string;
  Name: string;
  Institution_Number__c?: number | string;
  [key: string]: any;
}

/**
 * Gets the order value for an education history record
 * Uses Institution_Number__c if available, falls back to Name field for backward compatibility
 * @param record - Education history record
 * @returns The order value as a number
 */
export function getEducationHistoryOrder(record: EducationHistoryRecord): number {
  // First try to use Institution_Number__c if it exists and is valid
  if (record.Institution_Number__c !== undefined && record.Institution_Number__c !== null) {
    const institutionNumber = typeof record.Institution_Number__c === 'string' 
      ? parseInt(record.Institution_Number__c, 10) 
      : Number(record.Institution_Number__c);
    
    if (!isNaN(institutionNumber)) {
      return institutionNumber;
    }
  }
  
  // Fall back to Name field for backward compatibility
  const nameOrder = parseInt(record.Name, 10);
  return isNaN(nameOrder) ? 0 : nameOrder;
}

/**
 * Sorts education history records by their order
 * Uses Institution_Number__c if available, falls back to Name field for backward compatibility
 * @param records - Array of education history records
 * @returns Sorted array of education history records
 */
export function sortEducationHistoryByOrder(records: EducationHistoryRecord[]): EducationHistoryRecord[] {
  return records.sort((a, b) => {
    const orderA = getEducationHistoryOrder(a);
    const orderB = getEducationHistoryOrder(b);
    return orderA - orderB;
  });
}

/**
 * Finds an education history record by order value
 * Searches using both Institution_Number__c and Name fields for backward compatibility
 * @param records - Array of education history records
 * @param orderValue - The order value to search for (as string or number)
 * @returns The matching education history record or undefined
 */
export function findEducationHistoryByOrder(
  records: EducationHistoryRecord[],
  orderValue: string | number
): EducationHistoryRecord | undefined {
  const searchOrder = typeof orderValue === 'string' ? parseInt(orderValue, 10) : orderValue;

  if (isNaN(searchOrder)) {
    return undefined;
  }

  // First, try to find a record with Institution_Number__c matching the search order
  const foundByInstitutionNumber = records.find(record => {
    if (record.Institution_Number__c !== undefined && record.Institution_Number__c !== null) {
      const institutionNumber = typeof record.Institution_Number__c === 'string'
        ? parseInt(record.Institution_Number__c, 10)
        : Number(record.Institution_Number__c);

      return !isNaN(institutionNumber) && institutionNumber === searchOrder;
    }
    return false;
  });

  if (foundByInstitutionNumber) {
    return foundByInstitutionNumber;
  }

  // If not found by Institution_Number__c, fall back to Name field
  return records.find(record => {
    // Only check Name if Institution_Number__c is not available
    if (record.Institution_Number__c !== undefined && record.Institution_Number__c !== null) {
      return false; // Skip records that have Institution_Number__c
    }

    const nameOrder = parseInt(record.Name, 10);
    return !isNaN(nameOrder) && nameOrder === searchOrder;
  });
}

/**
 * Gets the order identifier string for an education history record
 * Returns Institution_Number__c if available, otherwise returns Name for backward compatibility
 * @param record - Education history record
 * @returns The order identifier as a string
 */
export function getEducationHistoryOrderIdentifier(record: EducationHistoryRecord): string {
  // First try to use Institution_Number__c if it exists and is valid
  if (record.Institution_Number__c !== undefined && record.Institution_Number__c !== null) {
    const institutionNumber = typeof record.Institution_Number__c === 'string' 
      ? parseInt(record.Institution_Number__c, 10) 
      : Number(record.Institution_Number__c);
    
    if (!isNaN(institutionNumber)) {
      return institutionNumber.toString();
    }
  }
  
  // Fall back to Name field for backward compatibility
  return record.Name;
}
