import { countriesData } from "./data/countryData";

/**
 * Service for converting between different country code formats
 * Supports conversion from Alpha-3 to Alpha-2 and Numeric to Alpha-2
 */
export class CountryCodeService {
  private static alpha3ToAlpha2Map: Map<string, string> = new Map();
  private static numericToAlpha2Map: Map<string, string> = new Map();
  private static alpha3ToCountryMap: Map<string, string> = new Map();
  private static isInitialized = false;

  /**
   * Initialize the lookup maps from the country data
   */
  private static initialize(): void {
    if (this.isInitialized) return;

    countriesData.forEach((country) => {
      // Alpha-3 to Alpha-2 mapping
      this.alpha3ToAlpha2Map.set(
        country.alpha3.toUpperCase(),
        country.alpha2.toUpperCase()
      );

      // Alpha-3 to Country name mapping
      this.alpha3ToCountryMap.set(
        country.alpha3.toUpperCase(),
        country.country.toUpperCase()
      );

      // Numeric to Alpha-2 mapping (handle both string and number formats)
      this.numericToAlpha2Map.set(
        country.numeric,
        country.alpha2.toUpperCase()
      );
      this.numericToAlpha2Map.set(
        parseInt(country.numeric).toString(),
        country.alpha2.toUpperCase()
      );
    });

    this.isInitialized = true;
  }

  /**
   * Convert Alpha-3 country code to Alpha-2
   * @param alpha3Code - 3-letter country code (e.g., 'PAK', 'USA')
   * @returns Alpha-2 country code (e.g., 'PK', 'US') or null if not found
   */
  public static getAlpha2FromAlpha3(alpha3Code: string): string | null {
    this.initialize();

    if (!alpha3Code || typeof alpha3Code !== "string") {
      return null;
    }

    const normalizedCode = alpha3Code.trim().toUpperCase();
    return this.alpha3ToAlpha2Map.get(normalizedCode) || null;
  }

  /**
   * Convert numeric country code to Alpha-2
   * @param numericCode - Numeric country code (e.g., '586' for Pakistan, '840' for USA)
   * @returns Alpha-2 country code (e.g., 'PK', 'US') or null if not found
   */
  public static getAlpha2FromNumeric(
    numericCode: string | number
  ): string | null {
    this.initialize();

    if (numericCode === null || numericCode === undefined) {
      return null;
    }

    const normalizedCode = numericCode.toString().trim();
    return this.numericToAlpha2Map.get(normalizedCode) || null;
  }

  /**
   * Get Alpha-2 country code from various input formats
   * Attempts to determine the input format and convert accordingly
   * @param countryCode - Country code in Alpha-2, Alpha-3, or numeric format
   * @returns Alpha-2 country code or null if not found/invalid
   */
  public static getAlpha2Code(countryCode: string | number): string | null {
    if (!countryCode) {
      return null;
    }

    const codeStr = countryCode.toString().trim();

    // If already Alpha-2 (2 characters), return as-is
    if (codeStr.length === 2 && /^[A-Za-z]{2}$/.test(codeStr)) {
      return codeStr.toUpperCase();
    }

    // If Alpha-3 (3 characters)
    if (codeStr.length === 3 && /^[A-Za-z]{3}$/.test(codeStr)) {
      return this.getAlpha2FromAlpha3(codeStr);
    }

    // If numeric (1-3 digits)
    if (/^\d{1,3}$/.test(codeStr)) {
      return this.getAlpha2FromNumeric(codeStr);
    }

    return null;
  }

  /**
   * Validate if a given code is a valid Alpha-2 country code
   * @param alpha2Code - 2-letter country code to validate
   * @returns boolean indicating if the code is valid
   */
  public static isValidAlpha2(alpha2Code: string): boolean {
    this.initialize();

    if (
      !alpha2Code ||
      typeof alpha2Code !== "string" ||
      alpha2Code.length !== 2
    ) {
      return false;
    }

    const normalizedCode = alpha2Code.trim().toUpperCase();
    return Array.from(this.alpha3ToAlpha2Map.values()).includes(normalizedCode);
  }

  /**
   * Get country details (name and alpha-2 code) from alpha-3 country code
   * @param alpha3Code - 3-letter country code (e.g., 'PAK', 'USA')
   * @returns Object with country name and alpha-2 code, or null if not found
   */
  public static getCountryDetails(alpha3Code: string): { 
    countryName: string; 
    alpha2Code: string; 
  } | null {
    this.initialize();

    if (!alpha3Code || typeof alpha3Code !== "string") {
      return null;
    }

    const normalizedCode = alpha3Code.trim().toUpperCase();
    const alpha2Code = this.alpha3ToAlpha2Map.get(normalizedCode);
    const countryName = this.alpha3ToCountryMap.get(normalizedCode);

    if (!alpha2Code || !countryName) {
      return null;
    }

    return {
      countryName: countryName,
      alpha2Code: alpha2Code,
    };
  }
}
