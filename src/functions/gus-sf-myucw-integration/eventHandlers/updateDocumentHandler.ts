import { LoggerService } from "src/common/cloudwatchService";
import {
  getRequiredDocuments,
  putMYUCWData,
} from "src/connectors/myucw-connector";
import { getData } from "src/connectors/eip-connector";
import { getPicklistValue } from "src/common/getPickListValue";
import { S3Service } from "src/common/s3Service";
import { LoggerEnum } from "@gus-eip/loggers";
import { EventHandler } from "../IEventHandler";
import { GUSErrorHandler } from "../utils/errorHandler";
import { getEducationHistoryOrderIdentifier } from "src/common/educationHistoryUtils";
const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();
const s3Service = new S3Service();
export class UpdateDocumentHandler implements EventHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  async fetchGusSFDetails(opportunityFileId: any): Promise<any> {
    try {
      await this.log(
        opportunityFileId,
        "get opportunity file initiated",
        loggerEnum.Event.GET_OPPORTUNITY_FILE_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      return await getData(
        `gus/opportunityfile/${opportunityFileId}`,
        this.correlationId,
        process.env.UCW_KEY
      );
    } catch (error) {
      await this.error(
        opportunityFileId,
        error,
        loggerEnum.Event.GET_OPPORTUNITY_FILE_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(
        `Error get opportunity file details ${opportunityFileId} : ${error}`
      );
    }
  }
  async syncInMyUCW(event: any): Promise<any> {
    try {
      return await putMYUCWData("students/requirements", event);
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.UPDATE_REQUIRED_DOCUMENT_FAILED,
        loggerEnum.Component.MY_UCW
      );
      throw new Error(
        `Error updating required document in MY UCW for ${event.Applic_Id__c} : ${error}`
      );
    }
  }
  async handleMessage(event: any): Promise<any> {
    const eventBody = JSON.parse(event.body);
    const platformEventMessage = JSON.parse(eventBody.Message);
    this.correlationId = platformEventMessage.event?.EventUuid;
    this.usecase = platformEventMessage?.payload?.Scenario__c;
    this.brand = platformEventMessage.payload.BusinessUnitFilter__c;
    let {
      DocumentType__c,
      Opportunity_file_Id__c,
      Applic_Id__c,
      BucketName__c,
      S3FileName__c,
      Name__c,
      Opportunity_Id__c,
    } = platformEventMessage.payload;
    this.applicationFormId =
      platformEventMessage.payload.Application_Form_Id__c;
    await this.log(
      platformEventMessage.payload,
      `MY UCW sync update required docs initiated ${Opportunity_file_Id__c}`,
      loggerEnum.Event.UPDATE_REQUIRED_DOC_INITIATED
    );
    if (
      DocumentType__c === "Degree transcripts" ||
      DocumentType__c === "Degree certificate" ||
      DocumentType__c === "High School Transcripts and Certificate"
    ) {
      const order = await this.getEduHistoryOrder(
        Opportunity_Id__c,
        Opportunity_file_Id__c
      );
      if (order) {
        DocumentType__c = `${DocumentType__c}_${order}`;
      }
    }
    try {
      const myUCWRequirementName = await getPicklistValue(
        "GUS_SF_MY_UCW",
        DocumentType__c,
        "requirement_name"
      );
      if (myUCWRequirementName) {
        let requiredDocuments;
        try {
          requiredDocuments = await getRequiredDocuments(Applic_Id__c);
        } catch (error) {
          await this.error(
            Applic_Id__c,
            error,
            loggerEnum.Event.GET_REQUIRED_DOCUMENT_FAILED,
            loggerEnum.Component.MY_UCW
          );
          throw new Error(
            `Error fetching required documents for ${Applic_Id__c} : ${error}`
          );
        }
        if (requiredDocuments?.length > 0) {
          await this.log(
            Applic_Id__c,
            "get required documents completed",
            loggerEnum.Event.GET_REQUIRED_DOCUMENTS_COMPLETED,
            loggerEnum.Component.MY_UCW,
            {},
            requiredDocuments
          );
          const requirement = requiredDocuments.find(function (element) {
            return element.requirement_name === myUCWRequirementName;
          });
          if (requirement) {
            // if (
            //   requirement.submitted === "false" ||
            //   requirement.status === "declined"
            // ) {
            await this.log(
              myUCWRequirementName,
              "get requirement completed",
              loggerEnum.Event.GET_REQUIREMENT_COMPLETED,
              loggerEnum.Component.MY_UCW,
              {},
              requirement
            );
            let base64File;
            try {
              base64File = await this.getS3FileData(
                S3FileName__c,
                BucketName__c
              );
            } catch (error) {
              await this.error(
                S3FileName__c,
                error,
                loggerEnum.Event.GET_BASE64_FAILED,
                loggerEnum.Component.OAP
              );
              throw new Error(
                `Error fetching Base64 data for ${S3FileName__c} : ${error}`
              );
            }
            if (base64File) {
              await this.log(
                S3FileName__c,
                "get base64 file completed",
                loggerEnum.Event.GET_BASE64_FILE_COMPLETED,
                loggerEnum.Component.OAP
              );
              const requirementId =
                requirement.student_requirement.match(/\d+/)[0];
              const response = await this.syncInMyUCW({
                student_requirement:
                  requirement.student_requirement.match(/\d+/)[0],
                files: [
                  {
                    type: "local",
                    filename: Name__c,
                    data: base64File,
                  },
                ],
              });
              await this.log(
                requirementId,
                "required document sync completed",
                loggerEnum.Event.OPERATION_COMPLETED,
                loggerEnum.Component.MY_UCW,
                {},
                JSON.stringify(response)
              );
              return {
                statusCode: 200,
                body: JSON.stringify(response),
              };
            } else {
              await this.log(
                platformEventMessage.payload,
                `Base64 Not found for ${DocumentType__c}`,
                loggerEnum.Event.OPERATION_COMPLETED,
                BucketName__c
              );

              return {
                status: true,
                message: `Base64 Not found for ${DocumentType__c}`,
              };
            }

            // } else {
            //   await this.log(
            //     myUCWRequirementName,
            //     `Requirement already submitted for ${myUCWRequirementName}`,
            //     "REQUIREMENT_ALREADY_SUBMITTED",
            //     "MY_UCW",
            //     {}
            //   );
            // }
          } else {
            console.log(
              `No matching requirement found for ${myUCWRequirementName}`
            );
            await this.log(
              myUCWRequirementName,
              `No matching requirement found for ${myUCWRequirementName}`,
              loggerEnum.Event.OPERATION_COMPLETED,
              loggerEnum.Component.MY_UCW,
              myUCWRequirementName,
              requirement
            );

            return {
              status: true,
              message: `No matching requirement found for ${myUCWRequirementName}`,
            };
          }
        } else {
          const error = `No required documents found for ${Applic_Id__c}`;
          // Check if this is the specific ignorable error for GUS_DOCUMENT_UPDATE
          if (GUSErrorHandler.isIgnorableError(error, this.usecase)) {
            await this.log(
              platformEventMessage.payload,
              GUSErrorHandler.getIgnoredErrorLogMessage(error, this.usecase),
              loggerEnum.Event.UPDATE_REQUIRED_DOC_FAILED,
              loggerEnum.Component.MY_UCW
            );
            return {
              status: true,
              message: "Document update ignored - no required documents found",
            };
          }

          throw new Error(error);
        }
      } else {
        await this.log(
          platformEventMessage.payload,
          `No matching requirement name found for ${DocumentType__c}`,
          loggerEnum.Event.OPERATION_COMPLETED,
          loggerEnum.Component.MY_UCW,
          platformEventMessage.payload,
          myUCWRequirementName
        );

        return {
          status: true,
          message: `No matching requirement name found for ${DocumentType__c}`,
        };
      }
    } catch (error) {
      await this.error(
        platformEventMessage,
        error.message ? error.message : error,
        loggerEnum.Event.UPDATE_REQUIRED_DOC_FAILED,
        loggerEnum.Component.MY_UCW
      );
      throw error.message ? error.message : error;
    }
  }
  async getS3FileData(fileKey, s3BucketName = null): Promise<any> {
    try {
      const key = decodeURIComponent(fileKey);
      return await s3Service.getBase64File(
        s3BucketName ? s3BucketName : process.env.REVIEW_CENTER_BUCKET_NAME,
        key,
        process.env.S3_CROSS_ACCOUNT_ACCESS_ROLE_ARN
      );
    } catch (error) {
      console.error("Error fetching file from S3:", error);
    }
  }
  async getEduHistoryOrder(opportunityId, opportunityFileId) {
    const opportunityfile = await this.fetchGusSFDetails(opportunityFileId);
    await this.log(
      opportunityFileId,
      "get opportunity file completed",
      loggerEnum.Event.GET_OPPORTUNITY_FILE_COMPLETED,
      loggerEnum.Component.GUS_SALESFORCE,
      {},
      opportunityfile
    );
    if (opportunityfile[0]?.Related_Education_History__c) {
      await this.log(
        opportunityId,
        "get education initiated",
        loggerEnum.Event.GET_EDU_HISTORY_INITIATED,
        loggerEnum.Component.MY_UCW
      );
      const educationHistory = await getData(
        `gus/geteducationhistorybyoppid/${opportunityId}`,
        this.correlationId,
        process.env.UCW_KEY
      );
      await this.log(
        opportunityId,
        "get education history completed",
        loggerEnum.Event.FETCH_EDU_HISTORY_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        {},
        educationHistory
      );
      const relatedEduHistory = educationHistory.find(
        (item) => item.Id === opportunityfile[0]?.Related_Education_History__c
      );
      if (relatedEduHistory) {
        // Use Institution_Number__c if available, otherwise fall back to Name for backward compatibility
        return getEducationHistoryOrderIdentifier(relatedEduHistory);
      }
      return undefined;
    }
  }
  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.MY_UCW,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.MY_UCW,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
