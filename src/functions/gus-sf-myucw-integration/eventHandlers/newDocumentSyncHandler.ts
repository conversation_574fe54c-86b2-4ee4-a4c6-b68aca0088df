import { LoggerService } from "src/common/cloudwatchService";
import {
  getRequiredDocuments,
  putMYUCWData,
} from "src/connectors/myucw-connector";
import { getData } from "src/connectors/eip-connector";
import { getPicklistByField } from "src/common/getPickListValue";
import { S3Service } from "src/common/s3Service";
import { LoggerEnum } from "@gus-eip/loggers";
import { EventHandler } from "../IEventHandler";
import { getEducationHistoryOrderIdentifier } from "src/common/educationHistoryUtils";
const loggerEnum = new LoggerEnum();
const loggerService = new LoggerService();
const s3Service = new S3Service();
export class NewDocumentSyncHandler implements EventHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  async fetchGusSFDetails(
    opportunityId: any,
    correlationId?: string
  ): Promise<any> {
    try {
      await this.log(
        opportunityId,
        `Fetch opportunity files for ${opportunityId}`,
        loggerEnum.Event.FETCH_OPPORTUNITY_FILES_INTIATED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      return await getData(
        `gus/getopportunityfiles/${opportunityId}`,
        correlationId,
        process.env.UCW_KEY
      );
    } catch (error) {
      await this.error(
        opportunityId,
        error,
        loggerEnum.Event.REQUIRED_DOCUMENT_SYNC_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(
        `Error fetching documents from gus by opportunityId: ${error}`
      );
    }
  }
  async syncInMyUCW(event: any): Promise<any> {
    try {
      await this.log(
        event?.student_requirement,
        "required document sync initiated",
        loggerEnum.Event.REQUIRED_DOCUMENT_SYNC_INITIATED,
        loggerEnum.Component.MY_UCW
      );
      return await putMYUCWData("students/requirements", event);
    } catch (error) {
      await this.error(
        event?.student_requirement,
        error,
        loggerEnum.Event.REQUIRED_DOCUMENT_SYNC_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error syncing in myucw: ${error}`);
    }
  }
  async buidSyncRequest(opportunityFiles, requiredDocuments): Promise<any> {
    const documentMapping = await getPicklistByField(
      "GUS_SF_MY_UCW",
      "requirement_name"
    );
    const response = {};
    for (let opportunityFile of opportunityFiles) {
      if (documentMapping[opportunityFile.DocumentType__c]) {
        const requirement = requiredDocuments.find(function (element) {
          return (
            element.requirement_name ===
            documentMapping[opportunityFile.DocumentType__c]
          );
        });
        if (requirement) {
          let base64File;
          try {
            base64File = await this.getS3FileData(
              opportunityFile.S3FileName__c,
              opportunityFile?.BucketName__c
            );
          } catch (error) {
            await this.error(
              opportunityFile.S3FileName__c,
              error,
              loggerEnum.Event.GET_BASE64_FAILED,
              loggerEnum.Component.OAP
            );
          }
          if (base64File) {
            if (response[requirement.student_requirement]) {
              response[requirement.student_requirement].files.push({
                type: "local",
                filename: opportunityFile.Name,
                data: base64File,
              });
            } else {
              response[requirement.student_requirement] = {
                student_requirement:
                  requirement.student_requirement.match(/\d+/)[0],
                files: [],
              };
              response[requirement.student_requirement].files.push({
                type: "local",
                filename: opportunityFile.Name,
                data: base64File,
              });
            }
          } else {
            await this.log(
              opportunityFile.S3FileName__c,
              `Base64 file not found for ${opportunityFile?.BucketName__c}`,
              loggerEnum.Event.BASE64_FILE_NOT_FOUND,
              loggerEnum.Component.OAP
            );
            console.log("Base64 file not found", opportunityFile.Name);
          }
        } else {
          console.log(
            "requirement not found for",
            opportunityFile.DocumentType__c
          );
        }
      } else {
        console.log("mapping not found for", opportunityFile.DocumentType__c);
      }
    }
    return response;
  }
  async getS3FileData(fileKey, s3BucketName = null): Promise<any> {
    try {
      const key = decodeURIComponent(fileKey);
      return await s3Service.getBase64File(
        s3BucketName ? s3BucketName : process.env.REVIEW_CENTER_BUCKET_NAME,
        key,
        process.env.S3_CROSS_ACCOUNT_ACCESS_ROLE_ARN
      );
    } catch (error) {
      console.error("Error fetching file from S3:", error);
    }
  }
  async mapEducationHistoryOrder(
    opportunityId,
    opportunityFiles
  ): Promise<any> {
    await this.log(
      opportunityId,
      "get education initiated",
      loggerEnum.Event.GET_EDU_HISTORY_INITIATED,
      loggerEnum.Component.MY_UCW
    );
    const educationHistory = await getData(
      `gus/geteducationhistorybyoppid/${opportunityId}`,
      this.correlationId,
      process.env.UCW_KEY
    );
    await this.log(
      opportunityId,
      "get education history completed",
      loggerEnum.Event.FETCH_EDU_HISTORY_COMPLETED,
      loggerEnum.Component.GUS_SALESFORCE,
      {},
      educationHistory
    );
    if (educationHistory.length > 0 && opportunityFiles.response.length > 0) {
      opportunityFiles.response.forEach((file) => {
        if (
          file.Related_Education_History__c &&
          (file.DocumentType__c === "Degree transcripts" ||
            file.DocumentType__c === "Degree certificate" ||
            file.DocumentType__c === "High School Transcripts and Certificate")
        ) {
          const relatedEduHistory = educationHistory.find(
            (history) => history.Id === file.Related_Education_History__c
          );
          if (relatedEduHistory) {
            // Use Institution_Number__c if available, otherwise fall back to Name for backward compatibility
            const orderIdentifier = getEducationHistoryOrderIdentifier(relatedEduHistory);
            file.DocumentType__c = `${file.DocumentType__c}_${orderIdentifier}`;
          }
        }
      });
    }
    await this.log(
      opportunityId,
      "education history mapping completed",
      loggerEnum.Event.EDU_HISTORY_MAPPING_COMPLETED,
      loggerEnum.Component.GUS_SALESFORCE,
      {},
      opportunityFiles
    );
    return opportunityFiles;
  }
  async handleMessage(event: any): Promise<any> {
    const eventBody = JSON.parse(event.body);
    const platformEventMessage = JSON.parse(eventBody.Message);
    this.correlationId = platformEventMessage.event?.EventUuid;
    this.usecase = platformEventMessage?.payload?.Scenario__c;
    this.applicationFormId =
      platformEventMessage.payload.Application_Form_Id__c;
    this.brand = platformEventMessage.payload.BusinessUnitFilter__c;
    const { sid, Opportunity_Id__c } = platformEventMessage.payload;
    await this.log(
      platformEventMessage.payload,
      `MY UCW sync required docs initiated ${Opportunity_Id__c}`,
      loggerEnum.Event.REQUIRED_DOCUMENT_SYNC
    );
    try {
      let opportunityFiles = await this.fetchGusSFDetails(Opportunity_Id__c);
      if (opportunityFiles?.response?.length > 0) {
        await this.log(
          Opportunity_Id__c,
          "get opportunity files completed",
          loggerEnum.Event.GUS_SF_FETCH_OPPFILE_COMPLETED,
          loggerEnum.Component.GUS_SALESFORCE,
          {},
          opportunityFiles
        );
        opportunityFiles = await this.mapEducationHistoryOrder(
          Opportunity_Id__c,
          opportunityFiles
        );
        const requiredDocuments = await getRequiredDocuments(sid);
        if (requiredDocuments?.length > 0) {
          await this.log(
            sid,
            "get required documents completed",
            loggerEnum.Event.MY_UCW_FETCH_REQUIREMENT_COMPLETED,
            loggerEnum.Component.MY_UCW,
            {},
            requiredDocuments
          );
          const requirementDocuments = await this.buidSyncRequest(
            opportunityFiles.response,
            requiredDocuments
          );
          if (Object.keys(requirementDocuments).length > 0) {
            await this.log(
              {},
              "get matching documents completed",
              loggerEnum.Event.GET_MATCHING_DOCUMENTS_COMPLETED,
              loggerEnum.Component.MY_UCW,
              requirementDocuments
            );
            const responses = [];
            for (let requirement in requirementDocuments) {
              responses.push(
                await this.syncInMyUCW(requirementDocuments[requirement])
              );
            }
            await this.log(
              sid,
              "required document sync completed",
              loggerEnum.Event.OPERATION_COMPLETED,
              loggerEnum.Component.MY_UCW,
              {},
              JSON.stringify(responses)
            );
            return {
              statusCode: 200,
              body: JSON.stringify(responses),
            };
          } else {
            await this.log(
              platformEventMessage.payload,
              `There is no matching requirement documents found in gus`,
              loggerEnum.Event.OPERATION_COMPLETED,
              loggerEnum.Component.MY_UCW,
              opportunityFiles.response,
              requiredDocuments
            );
            return {
              success: true,
              message: `There is no matching requirement documents found in gus`,
            };
          }
        } else {
          await this.log(
            platformEventMessage.payload,
            `There is no required documents for ${sid}`,
            loggerEnum.Event.OPERATION_COMPLETED,
            loggerEnum.Component.MY_UCW,
            platformEventMessage.payload,
            requiredDocuments
          );
          return {
            success: true,
            message: `There is no required documents for ${sid}`,
          };
        }
      } else {
        await this.log(
          platformEventMessage.payload,
          `There is no accepted opportunity files for ${Opportunity_Id__c}`,
          loggerEnum.Event.OPERATION_COMPLETED,
          loggerEnum.Component.GUS_SALESFORCE,
          platformEventMessage.payload,
          opportunityFiles
        );

        return {
          success: true,
          message: `There is no accepted opportunity files for ${Opportunity_Id__c}`,
        };
      }
    } catch (error) {
      await this.error(
        platformEventMessage,
        error.message ? error.message : error,
        loggerEnum.Event.REQUIRED_DOCUMENT_SYNC_FAILED,
        loggerEnum.Component.MY_UCW
      );
      throw error.message ? error.message : error;
    }
  }
  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.MY_UCW,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.MY_UCW,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
