import { PlatformEventHandlerFactory } from "./PlatformEventHandlerFactory";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import { storeFailedRecords, storeFailedRecordsByExternalApplicationId } from "src/common/storeFailedRecords";
import {
  checkExistingByExternalApplicationId,
  getRetryCountByExternalApplicationId,
} from "src/common/checkFailedRecords";
import { DynamoDBService } from "src/common/dynamodbService";
import { SnsService } from "src/common/snsService";
import { v4 as uuidv4 } from "uuid";
import { SQSEvent } from "aws-lambda";
import { BadRequestException } from "@nestjs/common";

// Global services and variables
const dbService = new DynamoDBService();
const snsService = new SnsService();
const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();

let correlationId: string;
let applicationFormId: string;
let brand = "ULAW";
let scenario = "";

export const handleOutboundIntegration = async (event: SQSEvent) => {
  const failedRecordsTableName =
    process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME;
  const responses: any[] = [];

  for (const record of event.Records) {
    try {
      console.log("event", JSON.stringify(record, null, 2));
      const eventBody = JSON.parse(record.body);
      const platformEvent = JSON.parse(eventBody.Message);
      platformEvent.eventId = record.messageId;
      console.log("platformEvent", platformEvent);
      const externalApplicationId = platformEvent.payload?.applicationDetails?.externalApplicationId;
      const isMessageGroupFailed = await checkExistingByExternalApplicationId(
        record,
        failedRecordsTableName,
        "ULAW_DCRM_GUS_SF"
      );

      scenario = platformEvent.scenario;
      correlationId = platformEvent.data?.correlationId || uuidv4();
      applicationFormId = externalApplicationId || platformEvent.data?.correlationId;

      if (isMessageGroupFailed === "No messages to process") {
        const handler = PlatformEventHandlerFactory.getHandler(scenario);
        const retryCount = await getRetryCountByExternalApplicationId(
          failedRecordsTableName,
          `ULAW_DCRM_GUS_SF`,
          externalApplicationId
        );
        console.log("Retry Count:", retryCount);
        if (!handler || typeof handler.handleMessage !== "function") {
          throw new Error(`No handler found for scenario: ${scenario}`);
        }

        const response = await handler.handleMessage(platformEvent, retryCount);
        responses.push(response);

        if (response && platformEvent.status === "Failed") {
          await dbService.deleteItem(failedRecordsTableName, {
            PK: `ULAW_DCRM_GUS_SF#${externalApplicationId}`,
            SK: platformEvent.uuid || record.messageId,
          });

          const remainingFailedRecords = await dbService.queryObjects({
            TableName: failedRecordsTableName,
            KeyConditionExpression: "PK = :pkValue",
            ExpressionAttributeValues: {
              ":pkValue": `ULAW_DCRM_GUS_SF#${externalApplicationId}`,
            },
          });

          if (remainingFailedRecords.Items.length === 0) {
            await dbService.deleteItem(failedRecordsTableName, {
              PK: "ULAW_DCRM_GUS_SF",
              SK: externalApplicationId,
            });
          }
        }
      } else {
        // Log and skip the record if external application ID already failed
        await loggerService.log(
          correlationId,
          new Date().toISOString(),
          loggerEnum.Component.GUS_SALESFORCE_ULAW_INTEGRATION_HANDLER,
          loggerEnum.Component.GUS_SALESFORCE_ULAW_INTEGRATION_QUEUE,
          loggerEnum.Component.GUS_SALESFORCE,
          loggerEnum.Event.OPERATION_COMPLETED,
          scenario,
          platformEvent,
          {},
          "A record with the same externalApplicationId failed earlier, so this record can't proceed further",
          brand,
          applicationFormId,
          "Application_Form_Id__c",
          applicationFormId,
          "",
          "",
          "",
          applicationFormId
        );
      }
    } catch (error) {
      // Skip DB store and detailed logging for known payload validation errors
      if (
        error instanceof BadRequestException &&
        error.message === "Payload validation failed"
      ) {
        return {
          statusCode: 400,
          message: "Invalid payload received. Skipped processing.",
        };
      }
      // Fallback in case external application ID check or parsing failed, or for records that need storing
      const eventBody = JSON.parse(record.body);
      const eventMessage = JSON.parse(eventBody.Message);
      const externalApplicationId = eventMessage.payload?.applicationDetails?.externalApplicationId;
      
      if (externalApplicationId) {
        await storeFailedRecordsByExternalApplicationId(
          record,
          failedRecordsTableName,
          "ULAW_DCRM_GUS_SF"
        );
      } else {
        // Fallback to original method if external application ID not found
        await storeFailedRecords(
          record,
          failedRecordsTableName,
          "ULAW_DCRM_GUS_SF"
        );
      }

      const fallbackMessage = {
        ...JSON.parse(record.body).data,
        correlationId,
      };

      await loggerService.error(
        correlationId,
        new Date().toISOString(),
        loggerEnum.Component.GUS_SALESFORCE_ULAW_INTEGRATION_HANDLER,
        loggerEnum.Component.GUS_SALESFORCE_ULAW_INTEGRATION_QUEUE,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.SYNC_APPLICATION_STATUS_UPDATE,
        scenario,
        fallbackMessage,
        fallbackMessage,
        error.message ? JSON.stringify(error.message) : JSON.stringify(error),
        brand,
        "",
        "Application_Form_Id__c",
        applicationFormId,
        "Opportunity",
        applicationFormId,
        "Opportunity",
        ""
      );
    }
  }

  return responses;
};

const deepClone = (obj) => {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }
  if (Array.isArray(obj)) {
    return obj.map((item) => deepClone(item));
  }
  const clonedObj = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      clonedObj[key] = deepClone(obj[key]);
    }
  }
  return clonedObj;
};

export const handleFailedRecords = async () => {
  const tableName = process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME;
  const topicArn = process.env.ULAW_OUTBOUND_TOPIC_ARN;

  try {
    const partitionParams = {
      TableName: tableName,
      KeyConditionExpression: "PK = :partitionKey",
      ExpressionAttributeValues: {
        ":partitionKey": "ULAW_DCRM_GUS_SF",
      },
    };

    const partitionResponse = await dbService.queryObjects(partitionParams);
    const partitionItems = partitionResponse?.Items || [];

    if (partitionItems.length === 0) {
      return "No records to process";
    }

    for (const partitionItem of partitionItems) {
      const retryCount = partitionItem.retryCount || 0;

      if (partitionItem.status !== "Failed" || retryCount > 3) {
        continue;
      }

      const messageGroupParams = {
        TableName: tableName,
        KeyConditionExpression: "PK = :partitionKey",
        ExpressionAttributeValues: {
          ":partitionKey": `${partitionItem.PK}#${partitionItem.SK}`,
        },
      };

      const messageGroupRecords = await dbService.queryObjects(
        messageGroupParams
      );
      const records = messageGroupRecords?.Items || [];

      for (const record of records) {
        try {
          const eventBody = JSON.parse(record.body);
          const eventMessage = JSON.parse(eventBody.Message);
          const externalApplicationId = eventMessage.payload?.applicationDetails?.externalApplicationId || partitionItem.SK;

          eventMessage.status = "Failed";

          console.log(
            "Event message before publishing:",
            eventMessage + "topicArn: " + topicArn
          );
          await snsService.publishMessages(
            eventMessage,
            externalApplicationId,
            topicArn
          );
        } catch (recordError) {
          console.error(
            "Failed to process individual record:",
            record,
            recordError
          );
          continue; // Skip to next record
        }
      }

      // Increment retry count at the group level
      try {
        await dbService.updateObject(
          tableName,
          {
            PK: partitionItem.PK,
            SK: partitionItem.SK,
          },
          {
            retryCount: retryCount + 1,
          }
        );
      } catch (updateError) {
        console.error(
          "Failed to update retry count for:",
          partitionItem,
          updateError
        );
      }
    }

    console.log("All records processed successfully");
    return "All records processed successfully";
  } catch (error) {
    console.error("Unexpected failure in handleFailedRecords:", error);
    throw new Error("Failed to handle retry logic for failed records.");
  }
};
