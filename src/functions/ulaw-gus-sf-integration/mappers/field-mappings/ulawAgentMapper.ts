export const ulawAgentFieldMapper = {
  // Lead mapping
  Lead: {
    // Personal information
    "accountDetails.firstName": "FirstName",
    "accountDetails.lastName": "LastName",
    "accountDetails.email": "Email",
    "accountDetails.phone.dialCode,accountDetails.phone.number": "Phone",
    "accountDetails.alternatePhone.dialCode,accountDetails.alternatePhone.number":
      "MobilePhone",

    // Address information
    "accountDetails.mailingAddress.street": "Street",
    "accountDetails.mailingAddress.city": "City",
    "accountDetails.mailingAddress.postalCode": "PostalCode",

    // Academic Program Details
    "leadDetails.locationOfInterest.name": "Location__c",
    // Note: Programme__c will be populated via API call in ApplicationHandler

    // Campaign & source
    "leadDetails.source": "LeadSource",

    // Agent linkage - These will be populated from getApplicationDtlsById response
    "applicationDetails.agentDetails.agentAccountId": "Agent_Account__c",
    "applicationDetails.agentDetails.agentContactId": "Agent_Contact__c",
    "applicationDetails.agentDetails.accountManagerUserId":
      "BusinessDeveloper__c",
    "applicationDetails.agentDetails.agentContactUserId": "OwnerId",
  },

  // Account (PersonAccount) mapping
  Account: {
    // Personal information
    "accountDetails.firstName": "FirstName",
    "accountDetails.lastName": "LastName",
    "accountDetails.email": "PersonEmail",
    "accountDetails.dob": "DateOfBirth__c",
    "accountDetails.gender": "Gender__c",
    "accountDetails.primaryLanguage": ["Primary_Language__c", "CommunicationLanguage__c"],

    // Phone numbers
    "accountDetails.phone.dialCode,accountDetails.phone.number": [
      "Mobile__c",
      "PersonMobilePhone",
    ],

    // PersonMailing address
    "accountDetails.mailingAddress.street": "PersonMailingStreet",
    "accountDetails.mailingAddress.city": "PersonMailingCity",
    "accountDetails.mailingAddress.postalCode": "PersonMailingPostalCode",

    // Agent linkage - These will be populated from getApplicationDtlsById response
    "applicationDetails.agentDetails.agentContactUserId": "OwnerId",
  },

  // Opportunity mapping
  Opportunity: {
    // Application identification
    "applicationDetails.externalApplicationId": "ApplicId__c",
    "applicationDetails.applicationSubmitted": "ApplicationSubmitted__c",
    "applicationDetails.stageName": "StageName",
    "applicationDetails.applicationProgressPercentage":
      "ApplicationProgress__c",
    "applicationDetails.applicantId": "Student_ID__c",

    // Course / intake information
    "applicationDetails.courseInfo.intake": ["Product_Intake_Date__c", "CloseDate"],
    "applicationDetails.courseInfo.courseName": "Name",

    // Agent linkage - These will be populated from getApplicationDtlsById response
    "applicationDetails.agentDetails.agentContactUserId": "OwnerId",
    "applicationDetails.agentDetails.accountManagerUserId":
      "BusinessDeveloper__c",
    "applicationDetails.agentDetails.agentAccountId": "Agent_Account__c",
    "applicationDetails.agentDetails.agentContactId": "Agent_Contact__c",
  },

  // Custom Application__c object mapping
  Application__c: {
    "applicationDetails.gusApplicationId": "Id",
  },
};
