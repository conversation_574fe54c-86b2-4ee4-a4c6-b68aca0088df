export const leadFieldMapper = {
  Lead: {
    // 🔹 Personal Information
    "accountDetails.firstName": "FirstName",
    "accountDetails.lastName": "LastName",
    "accountDetails.email": "Email",

    // 🔹 Contact Information
    "accountDetails.phone.dialCode,accountDetails.phone.number": "Phone",
    "accountDetails.mailingAddress.state": "State__c",
    "accountDetails.country.name": ["Country", "Country__c"],

    // 🔹 Academic Program Details
    "leadDetails.courseOfInterest.code": "Programme__c",
    "leadDetails.locationOfInterest.name": "Location__c",

    // 🔹 Agent & Ownership
    "applicationDetails.agentDetails.agentAccountId": "AgentAccount__c",
    "applicationDetails.agentDetails.agentContactId": "Agent_Contact__c",
    "applicationDetails.agentDetails.accountManagerUserId": "BusinessDeveloper__c",
    "applicationDetails.agentDetails.agentContactUserId": "OwnerId",

    // 🔹 Lead Source & Campaign
    "leadDetails.source": "LeadSource",
    "leadDetails.media": "Media__c",
    "leadDetails.leadType.code": "Lead_Type__c",
    "leadDetails.campaign.code": "Campaign__c",
    "leadDetails.event.code": "Event__c",
    "leadDetails.externalLeadId": "SourceId__c",

    // 🔹 Campus
    "leadDetails.campus.code": "Campus_Code__c",
    "leadDetails.campus.name": "Campus_Name__c",

    // 🔹 Business & Record Metadata (will be populated by custom fields)
    // "brand": "Brand__c",
    // "businessUnit": "BusinessUnit__c",
    // "businessUnitFilter": "BusinessUnitFilter__c",
    // "leadRecordTypeId": "RecordTypeId"
  }
};
