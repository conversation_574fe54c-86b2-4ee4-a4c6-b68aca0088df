export const ulawStudentFieldMapper = {
  // Lead mapping (student has no agent fields)
  Lead: {
    // Personal information
    "accountDetails.firstName": "FirstName",
    "accountDetails.lastName": "LastName",
    "accountDetails.email": "Email",
    "accountDetails.phone.dialCode,accountDetails.phone.number": "Phone",
    "accountDetails.alternatePhone.dialCode,accountDetails.alternatePhone.number":
      "MobilePhone",

    // Address information
    "accountDetails.mailingAddress.street": "Street",
    "accountDetails.mailingAddress.city": "City",
    "accountDetails.mailingAddress.postalCode": "PostalCode",

    // Academic Program Details
    // Note: Programme__c will be populated via API call in ApplicationHandler
    "leadDetails.locationOfInterest.name": "Location__c",

    // Campaign & source
    "leadDetails.source": "LeadSource",
  },

  // Account (PersonAccount) mapping
  Account: {
    // Personal information
    "accountDetails.firstName": "FirstName",
    "accountDetails.lastName": "LastName",
    "accountDetails.email": "PersonEmail",
    "accountDetails.dob": "DateOfBirth__c",
    "accountDetails.gender": "Gender__c",
    "accountDetails.primaryLanguage": ["Primary_Language__c", "CommunicationLanguage__c"],

    // Phone numbers
    "accountDetails.phone.dialCode,accountDetails.phone.number": [
      "Mobile__c",
      "PersonMobilePhone",
    ],

    // PersonMailing address
    "accountDetails.mailingAddress.street": "PersonMailingStreet",
    "accountDetails.mailingAddress.city": "PersonMailingCity",
    "accountDetails.mailingAddress.postalCode": "PersonMailingPostalCode",
  },

  // Opportunity mapping same as agent but without agent fields
  Opportunity: {
    // Application identification
    "applicationDetails.externalApplicationId": "ApplicId__c",
    "applicationDetails.applicationSubmitted": "ApplicationSubmitted__c",
    "applicationDetails.stageName": "StageName",
    "applicationDetails.applicationProgressPercentage":
      "ApplicationProgress__c",
    "applicationDetails.applicantId": "Student_ID__c",

    // Course / intake information
    "applicationDetails.courseInfo.courseName": "Name",
    "applicationDetails.courseInfo.intake": ["Product_Intake_Date__c", "CloseDate"],
  },
};
