import { LeadDetailsRequired } from "../dto/application.dto";

type ErrorDetail = {
  section: string;
  field: string;
  message: string;
};

export function validateLeadPayload(event: any): ErrorDetail[] {
  const errors: ErrorDetail[] = [];
  const payload = event.payload ?? {};

  // Validate mandatory lead fields
  LeadDetailsRequired.forEach((field) => {
    let value;
    
    switch (field) {
      case "email":
        value = payload.accountDetails?.email;
        break;
      case "lastName":
        value = payload.accountDetails?.lastName;
        break;
      case "country":
        value = payload.accountDetails?.country?.name;
        break;
      case "programme":
        value = payload.leadDetails?.courseOfInterest?.code;
        break;
      case "businessUnit":
        // This will be populated by custom fields
        value = "ULAW"; // Default for ULAW
        break;
      case "brand":
        // This will be populated by custom fields
        value = "ULAW"; // Default for ULAW
        break;
      default:
        value = payload.leadDetails?.[field];
        break;
    }

    if (!value || value === null || value === "") {
      errors.push({
        section: "leadDetails",
        field,
        message: `${field} is required for lead creation`,
      });
    }
  });

  // Basic account details validation for leads
  if (!payload.accountDetails?.firstName) {
    errors.push({
      section: "accountDetails",
      field: "firstName",
      message: "firstName is required for lead creation",
    });
  }

  return errors;
}
