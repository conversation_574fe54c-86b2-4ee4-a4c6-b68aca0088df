// Required fields validation for ULAW integration
export const RequiredFieldsValidation = {
  // Opportunity mandatory fields
  Opportunity: {
    requiredFields: ["StageName", "BusinessUnit__c", "Name"],
    mappedFields: {
      StageName: "applicationDetails.stageName",
      BusinessUnit__c: "customField", // Will be populated from salesforce metadata
      Name: "applicationDetails.courseInfo.courseName",
    },
  },

  // Account mandatory fields
  Account: {
    requiredFields: ["PersonEmail", "RecordTypeId", "LastName"],
    mappedFields: {
      PersonEmail: "accountDetails.email",
      RecordTypeId: "customField", // Will be populated from salesforce metadata
      LastName: "accountDetails.lastName",
    },
  },

  // Lead mandatory fields
  Lead: {
    requiredFields: [
      "Email",
      "BusinessUnit__c",
      "Brand__c",
      "LastName",
      "Country",
      "Programme__c",
    ],
    mappedFields: {
      Email: "accountDetails.email",
      BusinessUnit__c: "customField", // Will be populated from salesforce metadata
      Brand__c: "customField", // Will be populated from salesforce metadata
      LastName: "accountDetails.lastName",
      Country: "accountDetails.country.name",
      Programme__c: "applicationDetails.courseInfo.productId", // Will be validated and converted to Programme ID in ApplicationHandler
    },
  },
};

type ErrorDetail = {
  section: string;
  field: string;
  message: string;
  objectType: string;
};

export function validateRequiredFields(
  payload: any,
  objectType: "Lead" | "Account" | "Opportunity"
): ErrorDetail[] {
  const errors: ErrorDetail[] = [];
  const validation = RequiredFieldsValidation[objectType];

  if (!validation) {
    return errors;
  }

  validation.requiredFields.forEach((field) => {
    const mappedField = validation.mappedFields[field];

    if (mappedField === "customField") {
      // Skip custom fields - they will be populated automatically
      return;
    }

    // Special handling for Programme__c - only check if productId exists
    if (field === "Programme__c") {
      // Check if productId exists in payload
      const productId = payload.applicationDetails?.courseInfo?.productId;
      if (!productId || productId === null || productId === "") {
        errors.push({
          section: "applicationDetails",
          field: "applicationDetails.courseInfo.productId",
          message: `Product ID is required to fetch Programme ID for Lead creation`,
          objectType: objectType,
        });
      }
      return;
    }

    // Get the value from the payload using the mapped field path
    const value = mappedField
      .split(".")
      .reduce((obj, key) => obj?.[key], payload);

    if (!value || value === null || value === "") {
      errors.push({
        section: mappedField.split(".")[0],
        field: mappedField,
        message: `${field} is required for ${objectType} creation (mapped from ${mappedField})`,
        objectType: objectType,
      });
    }
  });

  return errors;
}
