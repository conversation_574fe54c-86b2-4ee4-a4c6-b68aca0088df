import { AckStatus } from "../enums/ack-status.enum";
import { AcknowledgmentService } from "../services/AcknowledgementService";

export async function handleErrorAcknowledgment(
  correlationId: string,
  usecase: string,
  event: any,
  error: any,
  details: Record<string, any>
) {
  console.error(
    "Handling error acknowledgement",
    JSON.stringify(details, null, 2)
  );
  const errorMessage =
    typeof error === "string" ? error : error.message || "Unknown error";
  await AcknowledgmentService.sendAcknowledgment(
    correlationId,
    usecase,
    AckStatus.FAILURE,
    errorMessage,
    details
  );
}
