import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import { CountryCodeService } from "src/common/countryCodeService";

import { v4 as uuid } from "uuid";
import { RegisterHandler } from "../HandlerRegistry";
import { AcknowledgmentService } from "../services/AcknowledgementService";
import { AckStatus } from "../enums/ack-status.enum";
import { ulawAgentFieldMapper } from "../mappers/field-mappings/ulawAgentMapper";
import { ulawStudentFieldMapper } from "../mappers/field-mappings/ulawStudentMapper";
import { salesforceCustomMetadata } from "../mappers/metadata/salesforce-custom-fields";
import { handleErrorAcknowledgment } from "../utils/AcknowledgementUtils";
import { validateRequiredFields } from "../validations/required-fields.validation";
import { BadRequestException } from "@nestjs/common";
const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();

@RegisterHandler("ULAW_NEW_APPLICATION")
export class ApplicationHandler implements IULAWOutboundEventHandler {
  private correlationId: string;
  private usecase: string = "ULAW_NEW_APPLICATION";
  private readonly brand: string = "ULAW";
  private isAgentApplication: boolean = false;
  private programmeId: string | null = null;

  async fetchGusSFDetails(_sid: string): Promise<any> {
    throw new Error("Method not implemented.");
  }

  /**
   * Generates Salesforce payload by mapping input data to SF fields
   * @param input - Input payload
   * @param config - Field mapping configuration
   * @returns Mapped SF payload
   */
  private generateSfPayload(
    input: Record<string, any>,
    config: any
  ): Record<string, any> {
    const result: Record<string, any> = {};

    for (const [objectName, fieldMap] of Object.entries(config)) {
      const sfObject: Record<string, any> = {};

      for (const [pathKey, sfFieldMapping] of Object.entries(fieldMap)) {
        const paths = pathKey.split(",").map((p) => p.trim());
        const values = paths
          .map((path) => this.getValueByPath(input, path))
          .filter((v) => v !== undefined && v !== null);

        let joinedValue = values.join(" ");

        if (Array.isArray(sfFieldMapping)) {
          // Assign same value to all SF fields in the array
          sfFieldMapping.forEach((sfField) => {
            sfObject[sfField] = joinedValue;
          });
        } else if (typeof sfFieldMapping === "string") {
          sfObject[sfFieldMapping] = joinedValue;
        } else {
          console.warn(`Invalid SF field mapping for key: ${pathKey}`);
        }
      }

      result[objectName] = sfObject;
    }

    return result;
  }

  /**
   * Helper to extract value from dot-path like 'accountDetails.firstName'
   * @param obj - Source object
   * @param path - Dot-separated path
   * @returns Value at the path
   */
  private getValueByPath(obj: any, path: string): any {
    return path.split(".").reduce((acc, key) => acc?.[key], obj);
  }

  /**
   * Extracts and merges agent details from API response
   * @param event - Event payload
   * @param applicationDetails - API response
   */
  private enrichAgentDetails(event: any, applicationDetails: any[]): void {
    if (applicationDetails.length === 0) {
      return;
    }

    const agentDetails = {
      agentContactUserId: applicationDetails[0].OwnerId,
      accountManagerUserId: applicationDetails[0].Business_Developer__c,
      agentAccountId: applicationDetails[0].Agent_Account__c,
      agentContactId: applicationDetails[0].Agent_Contact__c,
    };

    // Merge agent details into the payload
    event.payload.applicationDetails.agentDetails = {
      ...event.payload.applicationDetails.agentDetails,
      ...agentDetails,
    };
  }

  async syncToGus(applicationDetails: any): Promise<any> {
    try {
      const response = await postData(
        this.isAgentApplication
          ? "gus/ulaw/persistapplication"
          : "gus/ulaw/persiststudentapplication",
        applicationDetails,
        this.correlationId,
        "tHiTuzRZRn69TsmJP5gEX1sQGsxTsmA2mz1KzPqa"
      );

      return response;
    } catch (error) {
      throw new Error(`Error creating Application in GUS: ${error}`);
    }
  }

  /**
   * Gets Programme and Application details from product ID using the combined API
   * @param productId - The product ID to look up
   * @param applicationId - Optional application ID for agent applications
   * @returns Object containing programme and application details or null if not found/error
   */
  async fetchProgrammeAndApplicationDetails(
    productId: string,
    applicationId?: string
  ): Promise<{
    programmeId: string | null;
    applicationDetails: any[] | null;
  }> {
    try {
      if (!productId || productId.trim() === "") {
        return { programmeId: null, applicationDetails: null };
      }

      const businessUnit = encodeURIComponent("The University of Law");
      let apiUrl = `gus/getprogrammeandapplication?businessUnit=${businessUnit}&externalId=${productId}`;

      // Add applicationId for agent applications
      if (applicationId) {
        apiUrl += `&applicationId=${applicationId}`;
      }

      const response = await getData(
        apiUrl,
        this.correlationId,
        "tHiTuzRZRn69TsmJP5gEX1sQGsxTsmA2mz1KzPqa"
      );

      if (response && response.success) {
        const programmeId = response.programme?.Programme__c || null;
        const applicationDetails = response.application
          ? [response.application]
          : null;

        return { programmeId, applicationDetails };
      }

      return { programmeId: null, applicationDetails: null };
    } catch (error) {
      console.error(
        `Error fetching programme and application details for product ID ${productId}:`,
        error
      );
      return { programmeId: null, applicationDetails: null };
    }
  }

  async handleMessage(event: any, retryCount?: number): Promise<any> {
    console.log("🚀 Processing ULAW Application:", event);

    // Initialize handler state
    this.correlationId = event?.correlationId ?? uuid();
    this.usecase = event?.scenario ?? "ULAW_NEW_APPLICATION";
    this.isAgentApplication =
      event?.payload?.applicationDetails?.gusApplicationId;

    await this.log(
      event,
      `ULAW Application sync initiated - Agent: ${this.isAgentApplication}`,
      loggerEnum.Event.SYNC_IN_GUS_SF_INITIATED
    );

    // Validate payload
    const validationErrors = await this.validatePayload(event);
    if (validationErrors.length > 0) {
      await this.handleValidationErrors(event, validationErrors);
      throw new BadRequestException("Payload validation failed");
    }

    // Handle agent application details and fetch Programme ID for Lead creation
    await this.fetchAndValidateProgrammeId(event);

    try {
      // Generate and enrich SF payload
      const sfPayload = await this.generateSalesforcePayload(event);

      console.log(
        "📦 Generated SF Payload:",
        JSON.stringify(sfPayload, null, 2)
      );

      // Send to GUS
      const response = await this.syncToGus(sfPayload);

      console.log("✅ GUS Response:", JSON.stringify(response, null, 2));

      // Handle success
      await this.handleSuccess(event, response);

      return {
        success: true,
        payload: sfPayload,
        response: response,
        correlationId: this.correlationId,
      };
    } catch (error) {
      await this.handleError(event, error, retryCount);
      throw error;
    }
  }

  /**
   * Validates the event payload
   * @param event - Event to validate
   * @returns Array of validation errors
   */
  private async validatePayload(event: any): Promise<any[]> {
    const validationErrors: any[] = [];

    // Additional validation for required SF fields
    const opportunityErrors = validateRequiredFields(
      event.payload,
      "Opportunity"
    );
    const accountErrors = validateRequiredFields(event.payload, "Account");
    const leadErrors = validateRequiredFields(event.payload, "Lead");

    // Validate required application fields
    const payload = event.payload ?? {};

    // Check for externalApplicationId
    if (!payload.applicationDetails?.externalApplicationId) {
      validationErrors.push({
        section: "applicationDetails",
        field: "externalApplicationId",
        message: "externalApplicationId is required in applicationDetails",
      });
    }

    // Check for applicantId
    if (!payload.applicationDetails?.applicantId) {
      validationErrors.push({
        section: "applicationDetails",
        field: "applicantId",
        message: "applicantId is required in applicationDetails",
      });
    }

    // Validate intake date format (yyyy-mm-dd)
    const intake = payload.applicationDetails?.courseInfo?.intake;
    if (intake) {
      const datePattern = /^\d{4}-\d{2}-\d{2}$/;
      if (!datePattern.test(intake)) {
        validationErrors.push({
          section: "applicationDetails.courseInfo",
          field: "intake",
          message: "Intake date must be in yyyy-mm-dd format, received: " + intake,
        });
      } else {
        // Additional validation to ensure it's a valid date
        const date = new Date(intake);
        if (isNaN(date.getTime())) {
          validationErrors.push({
            section: "applicationDetails.courseInfo",
            field: "intake",
            message: "Invalid intake date provided: " + intake,
          });
        }
      }
    }

    return [
      ...validationErrors,
      ...opportunityErrors,
      ...accountErrors,
      ...leadErrors,
    ];
  }

  /**
   * Handles validation errors
   * @param event - Event being processed
   * @param errors - Validation errors
   */
  private async handleValidationErrors(
    event: any,
    errors: any[]
  ): Promise<void> {
    const errorResponse = { message: "Validation failed", errors };

    await handleErrorAcknowledgment(
      this.correlationId,
      this.usecase,
      event,
      errorResponse,
      errorResponse
    );

    await this.error(
      event,
      `Validation failed: ${JSON.stringify(errors)}`,
      loggerEnum.Event.VALIDATION_FAILED
    );
  }

  /**
   * Fetches and validates Programme ID for Lead creation
   * @param event - Event being processed
   */
  private async fetchAndValidateProgrammeId(event: any): Promise<void> {
    const productId = event?.payload?.applicationDetails?.courseInfo?.productId;

    if (!productId || productId.trim() === "") {
      const errorMsg =
        "Product ID is required to fetch Programme ID for Lead creation";
      await handleErrorAcknowledgment(
        this.correlationId,
        this.usecase,
        event,
        { message: errorMsg },
        { message: errorMsg }
      );

      await this.error(event, errorMsg, loggerEnum.Event.VALIDATION_FAILED);
      throw new BadRequestException(errorMsg);
    }

    // Get applicationId for agent applications
    const applicationId = this.isAgentApplication
      ? event?.payload?.applicationDetails?.gusApplicationId
      : undefined;

    // Fetch Programme ID and Application details from combined API
    const result = await this.fetchProgrammeAndApplicationDetails(
      productId,
      applicationId
    );

    if (!result.programmeId) {
      const errorMsg = `Failed to fetch Programme ID for product ID: ${productId}. Programme ID is mandatory for Lead creation.`;
      await handleErrorAcknowledgment(
        this.correlationId,
        this.usecase,
        event,
        { message: errorMsg },
        { message: errorMsg }
      );

      await this.error(event, errorMsg, loggerEnum.Event.VALIDATION_FAILED);
      throw new BadRequestException(errorMsg);
    }

    this.programmeId = result.programmeId;

    // For agent applications, check if application details were returned and enrich
    if (this.isAgentApplication) {
      if (
        !result.applicationDetails ||
        result.applicationDetails.length === 0
      ) {
        const errorMsg = "Application does not exist in Salesforce";
        await handleErrorAcknowledgment(
          this.correlationId,
          this.usecase,
          event,
          { message: errorMsg },
          { message: errorMsg }
        );

        await this.error(
          event,
          errorMsg,
          loggerEnum.Event.SYNC_IN_GUS_SF_FAILED
        );
        throw new BadRequestException(errorMsg);
      }

      this.enrichAgentDetails(event, result.applicationDetails);
    }

    console.log(
      `✅ Programme ID fetched: ${this.programmeId} for product ID: ${productId}`
    );
  }

  /**
   * Generates complete Salesforce payload
   * @param event - Event being processed
   * @returns Complete SF payload
   */
  private async generateSalesforcePayload(event: any): Promise<any> {
    const sfConfig = this.isAgentApplication
      ? ulawAgentFieldMapper
      : ulawStudentFieldMapper;

    // Normalize email to lowercase
    if (event?.payload?.accountDetails?.email) {
      event.payload.accountDetails.email = event.payload.accountDetails.email.toLowerCase();
    }

    // Generate mapped payload
    let payload = this.generateSfPayload(event.payload, sfConfig);

    // Handle country code conversion and enrich payload
    payload = this.enrichPayloadWithCountryData(payload, event.payload);

    // Inject Programme ID for Lead if available
    if (this.programmeId && payload.Lead) {
      payload.Lead.Programme__c = this.programmeId;
    }

    // Enrich with custom fields
    payload = this.enrichPayloadWithCustomFields(
      payload,
      process.env.stage || "dev"
    );

    // Add metadata with normalized email
    payload = {
      ...payload,
      email: event?.payload?.accountDetails?.email, // Already normalized above
      requestId: this.correlationId,
      sectionLabel: this.usecase,
      applicationId: event?.payload?.applicationDetails?.externalApplicationId,
      brand: this.brand,
    };

    return payload;
  }

  /**
   * Handles successful processing
   * @param event - Original event
   * @param response - GUS response
   */
  private async handleSuccess(event: any, response: any): Promise<void> {
    // Handle different response formats
    let details: any = {
      externalApplicationId:
        event?.payload?.applicationDetails?.externalApplicationId,
      gusApplicationId:
        event?.payload?.applicationDetails?.gusApplicationId || "",
    };

    // Check if response contains Salesforce IDs (successful creation response)
    if (response && typeof response === 'object' && (response.Account || response.Opportunity || response.Lead)) {
      details = {
        ...details,
        gusAccountId: response.Account || null,
        gusOpportunityId: response.Opportunity || null,
        gusLeadId: response.Lead || null,
      };
    } else if (response?.opportunityId) {
      // Legacy response format
      details.gusOpportunityId = response.opportunityId;
    }

    await AcknowledgmentService.sendAcknowledgment(
      this.correlationId,
      this.usecase,
      AckStatus.SUCCESS,
      "Application processed successfully",
      details
    );

    await this.log(
      event,
      `ULAW Application sync completed - Programme ID: ${this.programmeId}`,
      loggerEnum.Event.SYNC_IN_GUS_SF_COMPLETED
    );

    await this.log(
      event,
      `ULAW Application operation completed`,
      loggerEnum.Event.OPERATION_COMPLETED
    );
  }

  /**
   * Handles processing errors
   * @param event - Original event
   * @param error - Error that occurred
   * @param retryCount - Current retry count
   */
  private async handleError(
    event: any,
    error: any,
    retryCount?: number
  ): Promise<void> {
    await this.error(
      event,
      error,
      loggerEnum.Event.SYNC_IN_GUS_EVENT_FAILED,
      loggerEnum.Component.GUS_SALESFORCE
    );

    if (retryCount && retryCount > 3) {
      const details = {
        externalApplicationId:
          event?.payload?.applicationDetails?.externalApplicationId,
        gusApplicationId:
          event?.payload?.applicationDetails?.gusApplicationId || null,
      };

      await handleErrorAcknowledgment(
        this.correlationId,
        this.usecase,
        event,
        error,
        details
      );
    }
  }
  /**
   * Enriches the payload with custom fields for the given stage.
   *
   * This enriches the payload with the following custom fields:
   * - BusinessUnit__c
   * - RecordTypeId
   * - Brand__c
   *
   * The custom field values are determined by the stage.
   *
   * @param {Object} applicationDetails - The payload to enrich.
   * @param {String} stage - The stage of the payload.
   * @returns {Object} The enriched payload.
   */
  private enrichPayloadWithCustomFields(
    applicationDetails: any,
    stage: string
  ): any {
    const config =
      salesforceCustomMetadata[stage] || salesforceCustomMetadata.dev;
    const recordTypeIds = config.RECORD_TYPE_IDS;

    ["Lead", "Opportunity", "Account"].forEach((object) => {
      if (applicationDetails[object]) {
        applicationDetails[object] = {
          ...applicationDetails[object],
          BusinessUnit__c: config.BUSINESS_UNIT_ID,
          RecordTypeId: recordTypeIds[object],
        };

        if (["Lead", "Account"].includes(object)) {
          applicationDetails[object].Brand__c = "The University of Law";
        }
      }
    });

    // Set ApplicationSubmitted__c to boolean based on whether submittedDate exists
    if (applicationDetails.Opportunity) {
      const submittedDate = applicationDetails.Opportunity.CloseDate;
      applicationDetails.Opportunity.ApplicationSubmitted__c = Boolean(
        submittedDate && submittedDate.trim() !== ""
      );
    }

    return applicationDetails;
  }

  /**
   * Enriches the payload with country data using CountryCodeService
   * @param payload - The SF payload to enrich
   * @param sourcePayload - The original source payload
   * @returns The enriched payload with country information
   */
  private enrichPayloadWithCountryData(payload: any, sourcePayload: any): any {
    // Get country code from various possible locations in the source payload
    const countryCode = 
      sourcePayload?.accountDetails?.country?.code ||
      sourcePayload?.accountDetails?.country?.alpha3 ||
      sourcePayload?.accountDetails?.mailingAddress?.country?.code ||
      sourcePayload?.accountDetails?.mailingAddress?.country?.alpha3;

    if (countryCode && typeof countryCode === 'string') {
      const countryDetails = CountryCodeService.getCountryDetails(countryCode);
      
      if (countryDetails) {
        // Enrich Lead with country data
        if (payload.Lead) {
          payload.Lead.Country = countryDetails.countryName;
          payload.Lead.Country__c = countryDetails.countryName;
        }

        // Enrich Account with country data
        if (payload.Account) {
          payload.Account.Country__c = countryDetails.countryName;
          payload.Account.PersonMailingCountry = countryDetails.countryName;
          payload.Account.PersonMailingCountryCode = countryDetails.alpha2Code;
        }

        console.log(`✅ Country enrichment applied: ${countryCode} -> ${countryDetails.countryName} (${countryDetails.alpha2Code})`);
      } else {
        console.warn(`⚠️ Could not resolve country for code: ${countryCode}`);
      }
    } else {
      console.warn(`⚠️ No valid country code found in payload`);
    }

    return payload;
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    const applicId =
      sourcePayload?.applicationDetails?.externalApplicationId || "";
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      applicId,
      "ApplicId__c",
      applicId,
      destinationObject,
      destinationObjectId,
      "",
      applicId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destinationPayload
    );
    const applicId =
      sourcePayload?.applicationDetails?.externalApplicationId || "";
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      applicId,
      "ApplicId__c",
      applicId,
      destinationObject,
      destinationObjectId,
      "",
      applicId,
      response
    );
  }
}
