import { postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import { AcknowledgmentService } from "../services/AcknowledgementService";
import { AckStatus } from "../enums/ack-status.enum";
import { v4 as uuid } from "uuid";
import { RegisterHandler } from "../HandlerRegistry";
import { leadFieldMapper } from "../mappers/field-mappings/leadFieldMapper";
import { salesforceCustomMetadata } from "../mappers/metadata/salesforce-custom-fields";
import { handleErrorAcknowledgment } from "../utils/AcknowledgementUtils";
import { validateLeadPayload } from "../validations/lead.validation";
import { BadRequestException } from "@nestjs/common";

const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();

@RegisterHandler("ULAW_NEW_LEAD")
export class Create<PERSON>eadHandler implements IULAWOutboundEventHandler {
  private correlationId: string;
  private usecase: string = "ULAW_NEW_LEAD";
  private brand: string = "ULAW";
  private applicationFormId: string;
  
  async fetchGusSFDetails(_sid: string): Promise<any> {
    throw new Error("Method not implemented.");
  }

  private generateSfPayload(input: Record<string, any>, config: any) {
    const result: Record<string, any> = {};

    for (const [objectName, fieldMap] of Object.entries(config)) {
      const sfObject: Record<string, any> = {};

      for (const [pathKey, sfFieldMapping] of Object.entries(fieldMap)) {
        const paths = pathKey.split(",").map((p) => p.trim());
        const values = paths
          .map((path) => this.getValueByPath(input, path))
          .filter((v) => v !== undefined && v !== null);

        const joinedValue = values.join(" ");

        if (Array.isArray(sfFieldMapping)) {
          // Assign same value to all SF fields in the array
          sfFieldMapping.forEach((sfField) => {
            sfObject[sfField] = joinedValue;
          });
        } else if (typeof sfFieldMapping === "string") {
          sfObject[sfFieldMapping] = joinedValue;
        } else {
          console.warn(`Invalid SF field mapping for key: ${pathKey}`);
        }
      }

      result[objectName] = sfObject;
    }

    return result;
  }

  // Helper to extract value from dot-path like 'accountDetails.firstName'
  private getValueByPath(obj: any, path: string): any {
    return path.split(".").reduce((acc, key) => acc?.[key], obj);
  }

  private async updateCustomFields(payload: any): Promise<any> {
    try {
      const config =
        salesforceCustomMetadata[process.env.STAGE || "dev"] ||
        salesforceCustomMetadata.dev;
      const recordTypeIds = config.RECORD_TYPE_IDS;

      // Enrich Lead object with custom fields
      if (payload.Lead) {
        payload.Lead.BusinessUnit__c = config.BUSINESS_UNIT_ID;
        payload.Lead.Brand__c = "ULAW";
        payload.Lead.RecordTypeId = recordTypeIds["Lead"];
        payload.Lead.Application_Status__c = "New";
        payload.Lead.Status = "New";
        payload.Lead.Distribute_Automatically__c = 1;
        payload.Lead.started_application__c = false;
      }
      
      return payload;
    } catch (error) {
      throw new Error(`Error updating custom fields: ${error.message}`);
    }
  }

  async syncToGus(leadData: any): Promise<any> {
    try {
      const response = await postData(
        "gus/lead",
        leadData,
        this.correlationId,
        "tHiTuzRZRn69TsmJP5gEX1sQGsxTsmA2mz1KzPqa"
      );

      return response;
    } catch (error) {
      throw new Error(`Error creating lead in GUS: ${error}`);
    }
  }

  async handleMessage(event: any, retryCount?: number): Promise<any> {
    try {
      this.correlationId = event?.correlationId || uuid();
      this.usecase = event?.scenario || "ULAW_NEW_LEAD";
      this.applicationFormId = event?.payload?.leadDetails?.externalLeadId || uuid();
      
      await this.log(
        event,
        `ULAW Campusnet GUS SF Lead sync initiated`,
        loggerEnum.Event.SYNC_IN_GUS_SF_INITIATED
      );

      // Validate payload
      const validationErrors = validateLeadPayload(event);
      if (validationErrors.length > 0) {
        await handleErrorAcknowledgment(
          this.correlationId,
          this.usecase,
          event,
          { message: "Validation failed", errors: validationErrors },
          { message: "Validation failed", errors: validationErrors }
        );
        throw new BadRequestException("Payload validation failed");
      }

      // Generate SF payload using the new mapper
      let leadPayload = this.generateSfPayload(event.payload, leadFieldMapper);
      
      // Update with custom fields
      leadPayload = await this.updateCustomFields(leadPayload);
      
      // Add metadata
      leadPayload = {
        ...leadPayload,
        requestId: this.correlationId,
        sectionLabel: this.usecase,
      };

      console.log("Lead Payload:", leadPayload);

      const response = await this.syncToGus(leadPayload);
      const details = {
        gusLeadId: response?.id,
        externalLeadId: event?.payload?.leadDetails?.externalLeadId,
      };

      // Send acknowledgment after successful sync
      await AcknowledgmentService.sendAcknowledgment(
        this.correlationId,
        this.usecase,
        AckStatus.SUCCESS,
        "Lead processed successfully",
        details
      );
      await this.log(
        event,
        `ULAW Campusnet GUS SF Lead sync completed`,
        loggerEnum.Event.SYNC_IN_GUS_SF_COMPLETED
      );
      await this.log(
        event,
        `ULAW Campusnet GUS SF Lead sync completed`,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      return response;
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.SYNC_IN_GUS_EVENT_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      if (retryCount && retryCount > 3) {
        const details = {
          externalLeadId: event?.payload?.externalLeadId,
        };

        await handleErrorAcknowledgment(
          this.correlationId,
          this.usecase,
          event,
          error,
          details
        );
      }
      throw error;
    }
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destinationPayload
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
