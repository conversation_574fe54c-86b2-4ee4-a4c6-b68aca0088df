/**
 * @fileoverview Outbound Event Integration Service
 *
 * This service orchestrates the processing of outbound events from external systems
 * to target integrations. It provides a high-level interface for event processing
 * workflows and handles the coordination between different components.
 *
 * Key responsibilities:
 * - Coordinating event processing workflows
 * - Managing event processor instances
 * - Handling batch processing of SQS records
 * - Providing abstraction layer for Lambda handlers
 *
 * The service integrates with:
 * - EventProcessor for individual record processing
 * - Various event handlers for scenario-specific processing
 * - Logging and monitoring systems
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import {
  IEventRecord,
  IProcessingResult,
  IEventConfig,
  // Legacy compatibility types
  SQSRecord,
  ProcessingResult,
} from "../shared/types/interfaces";
import { logger } from "../shared/services/multiBrandCloudWatchLogger";
import { DEFAULT_CONFIG } from "../shared/config/config";
import { EventProcessor } from "./core/eventProcessor";

// Import handlers to ensure they register themselves
import "./handlers/enrollmentHandler";
import "./handlers/offerHandler";
import "./handlers/paymentHandler";
import "./handlers/rejectDocumentHandler";
import "./handlers/visaStatusUpdateHandler";
import "./handlers/closedLostHandler";
import "./handlers/applicationIntakeChangeHandler";
import "./handlers/additionalInformationRequiredHandler";
import "./handlers/additionalDocumentsRequiredHandler";

/**
 * Service class for orchestrating outbound event processing workflows.
 *
 * This class provides a high-level interface for processing events from
 * external systems and coordinating the various components involved in
 * event processing.
 */
export class OutboundEventIntegrationService {
  private readonly eventProcessor: EventProcessor;

  /**
   * Constructor for OutboundEventIntegrationService.
   *
   * Initializes the service with the provided configuration and sets up
   * the event processor with all necessary dependencies.
   *
   * @param {IEventConfig} config - Configuration for the service (optional, defaults to DEFAULT_CONFIG)
   */
  constructor(config: IEventConfig = DEFAULT_CONFIG) {
    this.eventProcessor = new EventProcessor(config);

    logger.info("Service initialized successfully with config:", {
      integrationPrefix: config.integrationPrefix,
      hasFailedRecordsTable: !!config.failedRecordsTableName,
      maxRetries: config.retryConfig.maxRetries,
    });
  }

  /**
   * Main entry point for processing outbound events from SQS.
   *
   * This method processes a batch of SQS records, each containing an event
   * that needs to be processed through the appropriate handler. It provides
   * comprehensive logging and error handling for the entire batch.
   *
   * Processing flow:
   * 1. Validates and logs the incoming event batch
   * 2. Delegates to EventProcessor for individual record processing
   * 3. Collects and aggregates results from all records
   * 4. Logs comprehensive statistics about the processing batch
   *
   * @param {Object} event - The SQS event containing records to process
   * @param {IEventRecord[]} event.Records - Array of SQS records to process
   * @returns {Promise<IProcessingResult[]>} Array of processing results for each record
   */
  public async processOutboundEvents(event: {
    Records: IEventRecord[];
  }): Promise<IProcessingResult[]> {
    logger.info(`Processing ${event.Records.length} outbound event records`);
    const startTime = Date.now();

    try {
      const results = await this.eventProcessor.processEvents(event);

      const totalTime = Date.now() - startTime;
      const successCount = results.filter((r) => r.status).length;
      const failureCount = results.length - successCount;

      logger.info(`Processing completed in ${totalTime}ms:`, {
        total: results.length,
        successful: successCount,
        failed: failureCount,
        averageTimePerRecord: Math.round(totalTime / results.length),
      });

      return results;
    } catch (error) {
      logger.error("Fatal error in processOutboundEvents:", error);
      throw error;
    }
  }

  /**
   * Process a single record (for testing or individual processing)
   */
  public async processRecord(record: IEventRecord): Promise<IProcessingResult> {
    logger.info(`Processing single record: ${record.messageId}`);
    return await this.eventProcessor.processRecord(record);
  }

  /**
   * Get handler registry information
   */
  public getHandlerRegistryInfo(): any {
    const registry = this.eventProcessor.getHandlerRegistry();
    return {
      totalHandlers: registry.getHandlerCount(),
      scenarios: registry.getRegisteredScenarios(),
    };
  }

  /**
   * Get current configuration
   */
  public getConfig(): IEventConfig {
    return this.eventProcessor.getConfig();
  }

  /**
   * Legacy compatibility method for processing SQS events.
   *
   * @deprecated Use processOutboundEvents instead
   * @param {Object} event - The SQS event containing records to process
   * @param {SQSRecord[]} event.Records - Array of SQS records to process
   * @returns {Promise<ProcessingResult[]>} Array of processing results for each record
   */
  public async handleSfRequests(event: {
    Records: SQSRecord[];
  }): Promise<ProcessingResult[]> {
    logger.warn(
      "handleSfRequests is deprecated. Use processOutboundEvents instead."
    );
    return await this.processOutboundEvents(event);
  }
}
