/**
 * @fileoverview Document Type Constants
 *
 * This module defines all valid document types used across the application.
 * These constants are used for validation in various event handlers to ensure
 * consistency and maintainability.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

/**
 * Array of all valid document types supported by the system
 * Used for validation across different event scenarios
 */
export const DOCUMENT_TYPE_VALUES = [
  "Application form",
  "ID/Passport",
  "ID/Passport/Visa",
  "Proof English proficiency",
  "Degree transcripts",
  "Degree certificate",
  "CV",
  "Reference letter",
  "Personal statement",
  "Other certification",
  "Photograph",
  "Proof Language proficiency",
  "Study Permit",
  "Visa",
  "Proof of Payment",
  "Work Permit",
  "Change Request Form",
  "Enrollment letter",
  "Proof of domestic status",
  "Payment Receipts",
  "Proof of Student Finance Application",
  "Study Contract",
  "Confirmation of Enrollment",
  "Biodata Change Request Form",
  "Deferral Change Request Form",
  "Withdrawal Change Request Form",
  "Visa Letter",
  "Proof of Deposit Payment",
  "Recommendation Letter",
  "High School Transcripts and Certificate",
  "Secondary School Transcripts and Certificate",
  "Student Loan Document",
  "Bank Letter",
  "Bank Statement",
  "Proof of Residency",
  "Passport information",
  "English Language Evidence",
  "Certificate of Deposit",
  "Letter of Consent from Parent or Guardian",
  "Current I20",
  "I94",
  "Student Finance Statements/Letters",
  "Higher Education Entrance Qualification",
  "Final Higher Education Entrance Qualification",
  "Foreign Credential Evaluation",
  "Work Experience Certificate",
  "Financial Statement Affidavit of Support",
  "Enrollment Agreement",
  "Accreditation Disclosure Form",
  "Consumer Information Disclosure Form",
  "International Student Statement of Understanding",
  "Unconditional Letter",
  "Medical Insurance",
  "Statement Of Purpose",
  "GMAT/ GRE",
  "GMAT - GRE",
  "IRCC Letter",
  "Scholarship Letter",
  "Visa Approval Letter",
  "Canadian Citizenship Certificate/Card",
] as const;

/**
 * Type definition for document types to ensure type safety
 */
export type DocumentType = (typeof DOCUMENT_TYPE_VALUES)[number];

/**
 * Document types specific to offer scenarios
 */
export const OFFER_DOCUMENT_TYPES = [
  "Offer letter",
  "Conditional Offer Letter",
] as const;

/**
 * Document types specific to visa scenarios
 */
export const VISA_DOCUMENT_TYPES = [
  "Visa",
  "Study Permit",
  "Work Permit",
  "Visa Letter",
  "Visa Approval Letter",
] as const;

/**
 * Document types for payment scenarios
 */
export const PAYMENT_DOCUMENT_TYPES = [
  "Proof of Payment",
  "Payment Receipts",
  "Proof of Deposit Payment",
  "Certificate of Deposit",
  "Bank Statement",
  "Bank Letter",
  "Student Finance Statements/Letters",
] as const;

/**
 * Document types for academic credentials
 */
export const ACADEMIC_DOCUMENT_TYPES = [
  "Degree transcripts",
  "Degree certificate",
  "High School Transcripts and Certificate",
  "Secondary School Transcripts and Certificate",
  "Higher Education Entrance Qualification",
  "Final Higher Education Entrance Qualification",
  "Foreign Credential Evaluation",
] as const;

/**
 * Helper function to validate if a document type is valid
 * @param documentType - The document type to validate
 * @returns boolean indicating if the document type is valid
 */
export function isValidDocumentType(
  documentType: string
): documentType is DocumentType {
  return DOCUMENT_TYPE_VALUES.includes(documentType as DocumentType);
}

/**
 * Helper function to get document types for a specific scenario
 * @param scenario - The scenario type
 * @returns Array of valid document types for the scenario
 */
export function getDocumentTypesForScenario(
  scenario: string
): readonly string[] {
  switch (scenario) {
    case "OFFER_STAGE":
      return OFFER_DOCUMENT_TYPES;
    case "VISA_STATUS_UPDATE":
      return VISA_DOCUMENT_TYPES;
    case "PAYMENT":
      return PAYMENT_DOCUMENT_TYPES;
    default:
      return DOCUMENT_TYPE_VALUES;
  }
}

/**
 * Helper function to validate document type for a specific scenario
 * @param documentType - The document type to validate
 * @param scenario - The scenario context
 * @returns boolean indicating if the document type is valid for the scenario
 */
export function isValidDocumentTypeForScenario(
  documentType: string,
  scenario: string
): boolean {
  const validTypes = getDocumentTypesForScenario(scenario);
  return validTypes.includes(documentType);
}
