/**
 * @fileoverview Additional Information Required Handler
 *
 * This module handles ADDITIONAL_INFORMATION_REQUIRED events for applications that need
 * additional information from applicants. It updates the Salesforce opportunity with the
 * required information details and marks the application as requiring additional info.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import {
  LOGGER_EVENTS,
  LOGGER_COMPONENTS,
} from "@functions/outbound-event-integration/shared/config/config";
import {
  IEventMessage,
  IHandlerResponse,
  IValidationResult,
} from "@functions/outbound-event-integration/shared/types/interfaces";
import { RegisterEventHandler } from "../core/eventHandlerRegistry";
import { scenarioValidator } from "../core/scenarioValidator";
import { AdditionalInformationRequiredValidationSchema } from "../validation/additionalInformationRequiredValidationSchema";
import { BaseGusSfHandler } from "./baseGusSfHandler";

@RegisterEventHandler(
  "ADDITIONAL_INFORMATION_REQUIRED",
  "Handles additional information required events for applications needing more info",
  "1.0.0"
)
export class AdditionalInformationRequiredHandler extends BaseGusSfHandler {
  constructor() {
    super();
    // Register the additional information required validation schema
    scenarioValidator.registerSchema(
      AdditionalInformationRequiredValidationSchema
    );
  }

  /**
   * Handles an additional information required message
   * @param message the event message to be handled
   * @returns the result of the handle operation
   */
  async handle(message: IEventMessage): Promise<IHandlerResponse> {
    try {
      // 1. Basic input validation
      this.validateInput(message);

      // 2. Scenario-specific validation
      const scenarioValidationResult = await this.validateScenario(message);
      if (!scenarioValidationResult.isValid) {
        return this.createValidationErrorResponse(scenarioValidationResult);
      }

      await this.logInfo(
        `Processing additional information required event for application: ${message.payload?.gusApplicationId}`,
        LOGGER_EVENTS.SYNC_IN_GUS_SF_INITIATED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        { scenario: message.scenario, correlationId: message.correlationId }
      );

      // 3. Fetch opportunity details
      const enrichedMessage = this.enrichMessageWithBrand(message);
      const opportunityResult = await this.fetchOpportunityDetails(
        enrichedMessage
      );

      // 4. FIRST: Create task for additional information (as per requirements)
      const taskResult = await this.createAdditionalInformationTask(
        opportunityResult.data.Id,
        enrichedMessage
      );

      // 5. THEN: Update opportunity with SF field changes
      const updatePayload = this.buildUpdatePayload(enrichedMessage);
      let updateResult = null;
      if (Object.keys(updatePayload).length > 0) {
        updateResult = await this.updateOpportunityDetails(
          opportunityResult.data.Id,
          updatePayload,
          enrichedMessage
        );
      }

      await this.logInfo(
        "Additional information required event processed successfully",
        LOGGER_EVENTS.SYNC_IN_GUS_SF_COMPLETED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        { scenario: message.scenario, correlationId: message.correlationId }
      );

      return {
        status: true,
        data: {
          processed: true,
          opportunityId: opportunityResult.data.Id,
          taskResult: taskResult.data,
          tasksCreated: taskResult.tasksProcessed,
          updatedFields: Object.keys(updatePayload),
          updateResult: updateResult?.data,
          handlerName: this.constructor.name,
          brand: enrichedMessage.brand,
        },
      };
    } catch (error) {
      await this.logError(
        `Additional information required processing failed: ${error.message}`,
        LOGGER_EVENTS.SYNC_IN_GUS_SF_FAILED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        { error: error.message, stack: error.stack }
      );

      return {
        status: false,
        error: error.message,
      };
    }
  }

  /**
   * Validates the message against additional information required-specific schema
   */
  private async validateScenario(
    message: IEventMessage
  ): Promise<IValidationResult> {
    const validationResult = scenarioValidator.validateScenario(
      "ADDITIONAL_INFORMATION_REQUIRED",
      message
    );

    if (!validationResult.isValid) {
      await this.logError(
        `Additional information required scenario validation failed: ${validationResult.errors
          .map((e) => `${e.field}: ${e.message}`)
          .join(", ")}`,
        LOGGER_EVENTS.VALIDATION_FAILED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        {
          validationErrors: validationResult.errors,
          scenario: message.scenario,
        }
      );
    }

    return validationResult;
  }

  /**
   * Creates a validation error response
   */
  private createValidationErrorResponse(
    validationResult: IValidationResult
  ): IHandlerResponse {
    const errorMessage = `Additional information required validation failed: ${validationResult.errors
      .map((e) => `${e.field}: ${e.message}`)
      .join(", ")}`;

    return {
      status: false,
      error: errorMessage,
      validationErrors: validationResult.errors,
    };
  }

  /**
   * Builds the additional information required update payload from the event message
   * Override the base class method to provide additional info-specific SF mapping
   */
  protected buildUpdatePayload(_message: IEventMessage): Record<string, any> {
    const payload: Record<string, any> = {};

    // Update the Documents field to indicate further clarification needed
    payload["documents__c"] = "Further Clarification Needed";

    return payload;
  }

  /**
   * Creates a task for additional information required
   * @param opportunityId - The opportunity ID to associate the task with
   * @param message - The event message containing the required information details
   * @returns Promise containing the task creation result
   */
  private async createAdditionalInformationTask(
    opportunityId: string,
    message: IEventMessage
  ) {
    // Extract required information from payload (not hardcoded)
    const requiredInformation =
      message.payload?.requiredInformation || "Not specified";

    // Build description from payload data as per your requirements
    const description = `Additional information needed | ${requiredInformation}`;

    // Create task data with standard subject and dynamic description
    const taskData = this.createTaskData(
      undefined, // Use default subject "Review center comments"
      description,
      opportunityId
    );

    return await this.createTasks([taskData], message);
  }
}
