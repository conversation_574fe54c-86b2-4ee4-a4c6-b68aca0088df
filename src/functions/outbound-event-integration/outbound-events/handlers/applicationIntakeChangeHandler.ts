/**
 * @fileoverview Application Intake Change Handler
 *
 * This module handles APPLICATION_INTAKE_CHANGE events for applications that have
 * changes to their intake date. It updates the Salesforce opportunity with the
 * new intake date and any associated comments.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import {
  LOGGER_EVENTS,
  LOGGER_COMPONENTS,
} from "@functions/outbound-event-integration/shared/config/config";
import {
  IEventMessage,
  IHandlerResponse,
  IValidationResult,
} from "@functions/outbound-event-integration/shared/types/interfaces";
import { RegisterEventHandler } from "../core/eventHandlerRegistry";
import { scenarioValidator } from "../core/scenarioValidator";
import { ApplicationIntakeChangeValidationSchema } from "../validation/applicationIntakeChangeValidationSchema";
import { BaseGusSfHandler } from "./baseGusSfHandler";

@RegisterEventHandler(
  "APPLICATION_INTAKE_CHANGE",
  "Handles application intake change events for intake date modifications",
  "1.0.0"
)
export class ApplicationIntakeChangeHandler extends BaseGusSfHandler {
  constructor() {
    super();
    // Register the application intake change validation schema
    scenarioValidator.registerSchema(ApplicationIntakeChangeValidationSchema);
  }

  /**
   * Handles an application intake change message
   * @param message the event message to be handled
   * @returns the result of the handle operation
   */
  async handle(message: IEventMessage): Promise<IHandlerResponse> {
    try {
      // 1. Basic input validation
      this.validateInput(message);

      // 2. Scenario-specific validation
      const scenarioValidationResult = await this.validateScenario(message);
      if (!scenarioValidationResult.isValid) {
        return this.createValidationErrorResponse(scenarioValidationResult);
      }

      await this.logInfo(
        `Processing application intake change event for application: ${message.payload?.gusApplicationId}`,
        LOGGER_EVENTS.SYNC_IN_GUS_SF_INITIATED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        { scenario: message.scenario, correlationId: message.correlationId }
      );

      // 3. Use the standard processing flow
      const syncResult = await this.processStandardFlow(message);

      await this.logInfo(
        "Application intake change event processed successfully",
        LOGGER_EVENTS.SYNC_IN_GUS_SF_COMPLETED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        { scenario: message.scenario, correlationId: message.correlationId }
      );

      return {
        status: true,
        data: syncResult,
      };
    } catch (error) {
      await this.logError(
        `Application intake change processing failed: ${error.message}`,
        LOGGER_EVENTS.SYNC_IN_GUS_SF_FAILED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        { error: error.message, stack: error.stack }
      );

      return {
        status: false,
        error: error.message,
      };
    }
  }

  /**
   * Validates the message against application intake change-specific schema
   */
  private async validateScenario(
    message: IEventMessage
  ): Promise<IValidationResult> {
    const validationResult = scenarioValidator.validateScenario(
      "APPLICATION_INTAKE_CHANGE",
      message
    );

    if (!validationResult.isValid) {
      await this.logError(
        `Application intake change scenario validation failed: ${validationResult.errors
          .map((e) => `${e.field}: ${e.message}`)
          .join(", ")}`,
        LOGGER_EVENTS.VALIDATION_FAILED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        {
          validationErrors: validationResult.errors,
          scenario: message.scenario,
        }
      );
    }

    return validationResult;
  }

  /**
   * Creates a validation error response
   */
  private createValidationErrorResponse(
    validationResult: IValidationResult
  ): IHandlerResponse {
    const errorMessage = `Application intake change validation failed: ${validationResult.errors
      .map((e) => `${e.field}: ${e.message}`)
      .join(", ")}`;

    return {
      status: false,
      error: errorMessage,
      validationErrors: validationResult.errors,
    };
  }

  /**
   * Builds the application intake change update payload from the event message
   * Override the base class method to provide intake change-specific SF mapping
   */
  protected buildUpdatePayload(message: IEventMessage): Record<string, any> {
    const payload: Record<string, any> = {};

    // Set the new intake date (could be empty string to clear the date)
    // If newIntakeDate is empty string, set to null to clear the field
    // Otherwise, set the new intake date
    payload["OverallStartDate__c"] = message.payload.newIntakeDate;

    // Add comment if provided
    if (message.payload?.comment) {
      payload["Admissions_Condition__c"] = message.payload.comment;
    }

    // Set the admissions stage to "Deferred" if applicable
    payload["AdmissionsStage__c"] = "Deferred";

    return payload;
  }
}
