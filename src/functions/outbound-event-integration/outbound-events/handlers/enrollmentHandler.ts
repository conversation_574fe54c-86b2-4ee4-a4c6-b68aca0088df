import {
  LOGGER_EVENTS,
  LOGGER_COMPONENTS,
} from "@functions/outbound-event-integration/shared/config/config";
import {
  IEventMessage,
  IHandlerResponse,
  IValidationResult,
} from "@functions/outbound-event-integration/shared/types/interfaces";
import { RegisterEventHandler } from "../core/eventHandlerRegistry";
import { scenarioValidator } from "../core/scenarioValidator";
import { EnrollmentValidationSchema } from "../validation/enrollmentValidationSchema";
import { BaseGusSfHandler } from "./baseGusSfHandler";

@RegisterEventHandler(
  "ENROLMENT_STAGE",
  "Handles enrollment stage events",
  "1.0.0"
)
export class EnrollmentHandler extends BaseGusSfHandler {
  constructor() {
    super();
    // Register the enrollment validation schema
    scenarioValidator.registerSchema(EnrollmentValidationSchema);
  }

  /**
   * Handles an enrollment message
   * @param message the event message to be handled
   * @returns the result of the handle operation
   */
  async handle(message: IEventMessage): Promise<IHandlerResponse> {
    try {
      // 1. Basic input validation
      this.validateInput(message);

      // 2. Scenario-specific validation
      const scenarioValidationResult = await this.validateScenario(message);
      if (!scenarioValidationResult.isValid) {
        return this.createValidationErrorResponse(scenarioValidationResult);
      }

      await this.logInfo(
        `Processing enrollment event for application: ${message.payload?.gusApplicationId}`,
        LOGGER_EVENTS.SYNC_IN_GUS_SF_INITIATED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        { scenario: message.scenario, correlationId: message.correlationId }
      );

      // 3. Use the standard processing flow
      const syncResult = await this.processStandardFlow(message);

      await this.logInfo(
        "Enrollment event processed successfully",
        LOGGER_EVENTS.SYNC_IN_GUS_SF_COMPLETED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        { scenario: message.scenario, correlationId: message.correlationId }
      );

      return {
        status: true,
        data: syncResult,
      };
    } catch (error) {
      await this.logError(
        `Enrollment processing failed: ${error.message}`,
        LOGGER_EVENTS.SYNC_IN_GUS_SF_FAILED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        { error: error.message, stack: error.stack }
      );

      return {
        status: false,
        error: error.message,
      };
    }
  }

  /**
   * Validates the message against enrollment-specific schema
   */
  private async validateScenario(
    message: IEventMessage
  ): Promise<IValidationResult> {
    const validationResult = scenarioValidator.validateScenario(
      "ENROLMENT_STAGE",
      message
    );

    if (!validationResult.isValid) {
      await this.logError(
        `Enrollment scenario validation failed: ${validationResult.errors
          .map((e) => `${e.field}: ${e.message}`)
          .join(", ")}`,
        LOGGER_EVENTS.VALIDATION_FAILED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        {
          validationErrors: validationResult.errors,
          scenario: message.scenario,
        }
      );
    }

    return validationResult;
  }

  /**
   * Creates a validation error response
   */
  private createValidationErrorResponse(
    validationResult: IValidationResult
  ): IHandlerResponse {
    const errorMessage = `Enrollment validation failed: ${validationResult.errors
      .map((e) => `${e.field}: ${e.message}`)
      .join(", ")}`;

    return {
      status: false,
      error: errorMessage,
      validationErrors: validationResult.errors,
    };
  }

  /**
   * Builds the enrollment update payload from the event message
   * Override the base class method to provide enrollment-specific SF mapping
   */
  protected buildUpdatePayload(message: IEventMessage): Record<string, any> {
    const payload: Record<string, any> = {};

    payload["StageName"] = "Closed Won"; // Assuming enrollment is a closed won stage
    payload["AdmissionsStage__c"] = message.payload.externalApplicationStatus; // Custom field for enrollment status

    // Add application ID reference
    if (message.payload?.externalApplicationId) {
      payload["ApplicId__c"] = message.payload.externalApplicationId;
    }

    return payload;
  }
}
