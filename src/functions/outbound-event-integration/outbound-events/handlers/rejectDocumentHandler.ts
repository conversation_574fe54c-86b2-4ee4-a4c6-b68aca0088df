import { BaseGusSfHandler } from "./baseGusSfHandler";
import { RegisterEventHandler } from "../core/eventHandlerRegistry";
import { IEventMessage, IHandlerResponse } from "../../shared/types/interfaces";
import { LOGGER_EVENTS, LOGGER_COMPONENTS } from "../../shared/config/config";
import { scenarioValidator } from "../core/scenarioValidator";
import { RejectDocumentValidationSchema } from "../validation/rejectDocumentValidationSchema";
import { globalConfigContext } from "../../shared/services/globalConfigContext";
import { getData, postData } from "src/connectors/eip-connector";

@RegisterEventHandler(
  "REJECT_DOCUMENT",
  "Handles document rejection events",
  "1.0.0"
)
export class RejectDocumentHandler extends BaseGusSfHandler {
  constructor() {
    super();
    // Register validation schema for this scenario
    scenarioValidator.registerSchema(RejectDocumentValidationSchema);
  }

  /**
   * Handles a reject document message with validation
   * @param message the event message to be handled
   * @returns the result of the handle operation
   */
  async handle(message: IEventMessage): Promise<IHandlerResponse> {
    try {
      this.validateInput(message);

      // Perform scenario-specific validation
      const validationResult = scenarioValidator.validateScenario(
        message.scenario,
        message
      );
      if (!validationResult.isValid) {
        const validationErrors = validationResult.errors.map((error) => ({
          field: error.field,
          message: error.message,
        }));

        return {
          status: false,
          error: `Reject document validation failed: ${validationResult.errors
            .map((e) => `${e.field}: ${e.message}`)
            .join(", ")}`,
          validationErrors,
        };
      }

      await this.logInfo(
        `Processing reject document event for application: ${message.payload?.gusApplicationId}`,
        LOGGER_EVENTS.SYNC_IN_GUS_SF_INITIATED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE
      );

      // Use the standard flow from BaseGusSfHandler to get opportunity details
      const processResult = await this.processStandardFlow(message);

      const opportunityFileDetails = await this.fetchOpportunityFileDetails(
        message.payload.files.filePath,
        processResult.opportunityId,
        message.payload.files.documentType,
        message.correlationId,
        message.brand
      );

      if (!opportunityFileDetails || !opportunityFileDetails.Id) {
        const errorMessage = `No opportunity file found for document type: ${message.payload.files.documentType} with path: ${message.payload.files.filePath}`;

        await this.logError(
          errorMessage,
          LOGGER_EVENTS.SYNC_IN_GUS_SF_FAILED,
          LOGGER_COMPONENTS.GUS_EIP_SERVICE
        );

        return {
          status: false,
          error: errorMessage,
        };
      }

      await this.logInfo(
        `Opportunity file found: ${opportunityFileDetails.Id}`,
        LOGGER_EVENTS.FETCH_OPPORTUNITY_COMPLETED, // Generic fetch completion event
        LOGGER_COMPONENTS.GUS_EIP_SERVICE
      );

      // Create rejection payload
      const publishEventPayload = {
        Brand__c: message.brand,
        Document_Status__c: "Rejected",
        Opportunity_File_Id__c: opportunityFileDetails.Id,
        Opportunity_Id__c: processResult.opportunityId,
        Rejection_Comments__c: message.payload.files.comment,
      };

      // Sync rejection to GUS SF
      await this.logInfo(
        `Publishing reject document event for file: ${message.payload.files.filename}`,
        LOGGER_EVENTS.SYNC_IN_GUS_SF_INITIATED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE
      );

      const publishResponse = await this.syncRejectionToGUS(
        publishEventPayload,
        message.correlationId,
        message.brand
      );

      await this.logInfo(
        `Reject document event published successfully`,
        LOGGER_EVENTS.SYNC_IN_GUS_SF_COMPLETED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE
      );

      return {
        status: true,
        data: {
          applicationFormId: message.payload.gusApplicationId,
          opportunityId: processResult.opportunityId,
          opportunityFileId: opportunityFileDetails.Id,
          processedAt: new Date().toISOString(),
          scenario: message.scenario,
          documentType: message.payload.files.documentType,
          filename: message.payload.files.filename,
          rejectionReason: message.payload.files.comment,
          publishResponse: publishResponse,
          message: "Reject document handler executed successfully",
        },
      };
    } catch (error) {
      await this.logError(
        `Error processing reject document event: ${error.message}`,
        LOGGER_EVENTS.SYNC_IN_GUS_SF_FAILED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        error
      );

      return {
        status: false,
        error: error.message,
      };
    }
  }

  /**
   * Required implementation of abstract method from BaseGusSfHandler
   * For reject document, we don't need to update the opportunity
   */
  protected buildUpdatePayload(_message: IEventMessage): Record<string, any> {
    // For reject document scenario, we don't update the opportunity directly
    // The rejection is handled through the publishrejecteddocumentevent API
    return {};
  }

  /**
   * Fetch opportunity file details using either s3FileName or fileName based on brand
   * @param filePath - File path or filename
   * @param opportunityId - SF Opportunity ID
   * @param documentType - Document type
   * @param correlationId - Correlation ID for logging
   * @param brand - Brand (ARD uses fileName, others use s3FileName)
   * @returns Opportunity file details
   */
  private async fetchOpportunityFileDetails(
    filePath: string,
    opportunityId: string,
    documentType: string,
    correlationId: string,
    brand: string
  ): Promise<any> {
    try {
      await this.logInfo(
        `Fetching opportunity file by ${
          brand === "ARD" ? "filename" : "s3FileName"
        } initiated`,
        LOGGER_EVENTS.SYNC_IN_GUS_SF_INITIATED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE
      );

      const apiKey = globalConfigContext.getApiKey();

      // Build API endpoint based on brand
      let endpoint: string;
      if (brand === "ARD") {
        // For Arden, use fileName parameter
        endpoint = `gus/opportunityfilesByS3Filename?fileName=${encodeURIComponent(
          filePath
        )}&opportunityId=${opportunityId}&documentType=${encodeURIComponent(
          documentType
        )}&scenario=REJECT_DOCUMENT`;
      } else {
        // For other brands, use s3FileName parameter
        endpoint = `gus/opportunityfilesByS3Filename?s3FileName=${encodeURIComponent(
          filePath
        )}&opportunityId=${opportunityId}&documentType=${encodeURIComponent(
          documentType
        )}&scenario=REJECT_DOCUMENT`;
      }

      const opportunityFileData = await getData(
        endpoint,
        correlationId,
        apiKey
      );

      await this.logInfo(
        `Opportunity file fetch completed`,
        LOGGER_EVENTS.SYNC_IN_GUS_SF_COMPLETED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE
      );

      return opportunityFileData;
    } catch (error) {
      await this.logError(
        `Error fetching opportunity file details: ${error.message}`,
        LOGGER_EVENTS.SALESFORCE_REQUEST_COMPLETED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        error
      );
      throw new Error(
        `Error fetching opportunity file details: ${error.message}`
      );
    }
  }

  /**
   * Sync rejection event to GUS Salesforce
   * @param eventPayload - Rejection event payload
   * @param correlationId - Correlation ID for logging
   * @param brand - Brand for API key
   * @returns Sync result
   */
  private async syncRejectionToGUS(
    eventPayload: any,
    correlationId: string,
    brand: string
  ): Promise<any> {
    try {
      const apiKey = globalConfigContext.getApiKey();

      await this.logInfo(
        `Syncing rejection to GUS SF: ${eventPayload.Opportunity_File_Id__c}`,
        LOGGER_EVENTS.SYNC_IN_GUS_SF_INITIATED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE
      );

      const publishResponse = await postData(
        "gus/publishrejecteddocumentevent",
        eventPayload,
        correlationId,
        apiKey
      );

      return publishResponse;
    } catch (error) {
      throw new Error(`Error syncing rejection to GUS: ${error.message}`);
    }
  }
}
