/**
 * @fileoverview Additional Documents Required Handler
 *
 * This module handles ADDITIONAL_DOCUMENTS_REQUIRED events for applications that need
 * additional documents from applicants. It updates the Salesforce opportunity with the
 * required document types and marks the application as requiring additional documents.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import {
  LOGGER_EVENTS,
  LOGGER_COMPONENTS,
} from "@functions/outbound-event-integration/shared/config/config";
import {
  IEventMessage,
  IHandlerResponse,
  IValidationResult,
} from "@functions/outbound-event-integration/shared/types/interfaces";
import { RegisterEventHandler } from "../core/eventHandlerRegistry";
import { scenarioValidator } from "../core/scenarioValidator";
import { AdditionalDocumentsRequiredValidationSchema } from "../validation/additionalDocumentsRequiredValidationSchema";
import { BaseGusSfHandler } from "./baseGusSfHandler";

@RegisterEventHandler(
  "ADDITIONAL_DOCUMENTS_REQUIRED",
  "Handles additional documents required events for applications needing more documents",
  "1.0.0"
)
export class AdditionalDocumentsRequiredHandler extends BaseGusSfHandler {
  constructor() {
    super();
    // Register the additional documents required validation schema
    scenarioValidator.registerSchema(
      AdditionalDocumentsRequiredValidationSchema
    );
  }

  /**
   * Handles an additional documents required message
   * @param message the event message to be handled
   * @returns the result of the handle operation
   */
  async handle(message: IEventMessage): Promise<IHandlerResponse> {
    try {
      // 1. Basic input validation
      this.validateInput(message);

      // 2. Scenario-specific validation
      const scenarioValidationResult = await this.validateScenario(message);
      if (!scenarioValidationResult.isValid) {
        return this.createValidationErrorResponse(scenarioValidationResult);
      }

      await this.logInfo(
        `Processing additional documents required event for application: ${message.payload?.gusApplicationId}`,
        LOGGER_EVENTS.SYNC_IN_GUS_SF_INITIATED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        { scenario: message.scenario, correlationId: message.correlationId }
      );

      // 3. Fetch opportunity details
      const enrichedMessage = this.enrichMessageWithBrand(message);
      const opportunityResult = await this.fetchOpportunityDetails(
        enrichedMessage
      );

      // 4. FIRST: Create tasks for additional documents (as per requirements)
      const taskResult = await this.createAdditionalDocumentsTasks(
        opportunityResult.data.Id,
        enrichedMessage
      );

      // 5. THEN: Update opportunity with SF field changes
      const updatePayload = this.buildUpdatePayload(enrichedMessage);
      let updateResult = null;
      if (Object.keys(updatePayload).length > 0) {
        updateResult = await this.updateOpportunityDetails(
          opportunityResult.data.Id,
          updatePayload,
          enrichedMessage
        );
      }

      await this.logInfo(
        "Additional documents required event processed successfully",
        LOGGER_EVENTS.SYNC_IN_GUS_SF_COMPLETED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        { scenario: message.scenario, correlationId: message.correlationId }
      );

      return {
        status: true,
        data: {
          processed: true,
          opportunityId: opportunityResult.data.Id,
          taskResult: taskResult.data,
          tasksCreated: taskResult.tasksProcessed,
          updatedFields: Object.keys(updatePayload),
          updateResult: updateResult?.data,
          handlerName: this.constructor.name,
          brand: enrichedMessage.brand,
        },
      };
    } catch (error) {
      await this.logError(
        `Additional documents required processing failed: ${error.message}`,
        LOGGER_EVENTS.SYNC_IN_GUS_SF_FAILED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        { error: error.message, stack: error.stack }
      );

      return {
        status: false,
        error: error.message,
      };
    }
  }

  /**
   * Validates the message against additional documents required-specific schema
   */
  private async validateScenario(
    message: IEventMessage
  ): Promise<IValidationResult> {
    const validationResult = scenarioValidator.validateScenario(
      "ADDITIONAL_DOCUMENTS_REQUIRED",
      message
    );

    if (!validationResult.isValid) {
      await this.logError(
        `Additional documents required scenario validation failed: ${validationResult.errors
          .map((e) => `${e.field}: ${e.message}`)
          .join(", ")}`,
        LOGGER_EVENTS.VALIDATION_FAILED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        {
          validationErrors: validationResult.errors,
          scenario: message.scenario,
        }
      );
    }

    return validationResult;
  }

  /**
   * Creates a validation error response
   */
  private createValidationErrorResponse(
    validationResult: IValidationResult
  ): IHandlerResponse {
    const errorMessage = `Additional documents required validation failed: ${validationResult.errors
      .map((e) => `${e.field}: ${e.message}`)
      .join(", ")}`;

    return {
      status: false,
      error: errorMessage,
      validationErrors: validationResult.errors,
    };
  }

  /**
   * Builds the additional documents required update payload from the event message
   * Override the base class method to provide additional documents-specific SF mapping
   */
  protected buildUpdatePayload(_message: IEventMessage): Record<string, any> {
    const payload: Record<string, any> = {};

    // Update the Documents field to indicate further clarification needed
    payload["documents__c"] = "Further Clarification Needed";

    return payload;
  }

  /**
   * Creates tasks for additional documents required
   * @param opportunityId - The opportunity ID to associate the tasks with
   * @param message - The event message containing the required document types
   * @returns Promise containing the task creation result
   */
  private async createAdditionalDocumentsTasks(
    opportunityId: string,
    message: IEventMessage
  ) {
    // Extract required document types from payload (not hardcoded)
    const requiredDocumentTypes = message.payload?.requiredDocumentTypes || [];

    // If no document types specified, create a single generic task
    if (
      !Array.isArray(requiredDocumentTypes) ||
      requiredDocumentTypes.length === 0
    ) {
      const description = "Additional Documents needed | Not specified";
      const taskData = this.createTaskData(
        undefined, // Use default subject "Review center comments"
        description,
        opportunityId
      );
      return await this.createTasks([taskData], message);
    }

    // Create a task for each required document type with payload data
    const tasksData = requiredDocumentTypes.map((documentType: string) => {
      const description = `Additional Documents needed | ${documentType}`;
      return this.createTaskData(
        undefined, // Use default subject "Review center comments"
        description,
        opportunityId
      );
    });

    return await this.createTasks(tasksData, message);
  }
}
