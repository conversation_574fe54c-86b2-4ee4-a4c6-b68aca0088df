/**
 * @fileoverview GUS Salesforce Task Service
 *
 * This service provides centralized task creation functionality for GUS Salesforce operations.
 * It handles single task creation and bulk task creation with proper logging and error handling.
 *
 * Key features:
 * - Single task creation
 * - Bulk task creation for efficiency
 * - Comprehensive logging with CloudWatch integration
 * - Error handling and validation
 * - Brand-aware operations
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { postData } from "src/connectors/eip-connector";
import { IEventLogger } from "../../shared/types/interfaces";
import { LOGGER_EVENTS, LOGGER_COMPONENTS } from "../../shared/config/config";
import { globalConfigContext } from "../../shared/services/globalConfigContext";

/**
 * Interface for task creation data
 */
export interface ITaskData {
  Subject: string;
  Description: string;
  WhatId: string;
  Status?: string;
  Priority?: string;
}

/**
 * Interface for task service operation results
 */
export interface ITaskOperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  correlationId: string;
  tasksProcessed?: number;
}

/**
 * Interface for task service operation options
 */
export interface ITaskOperationOptions {
  correlationId: string;
  scenario: string;
  brand: string;
  applicationFormId: string;
  timeout?: number;
  retryAttempts?: number;
}

/**
 * Centralized service for GUS Salesforce task operations
 */
export class GusTaskService {
  private static readonly SINGLE_TASK_THRESHOLD = 1;
  private static readonly DEFAULT_TASK_STATUS = "Open";
  private static readonly DEFAULT_TASK_PRIORITY = "High";
  private static readonly DEFAULT_TASK_SUBJECT = "Review center comments";

  constructor() {
    // Service initialized
  }

  /**
   * Creates a single task in Salesforce
   *
   * @param taskData - The task data to create
   * @param options - Operation options including correlation ID, scenario, brand
   * @param logger - Event logger for logging operations
   * @returns Promise containing the task creation result
   */
  async createSingleTask(
    taskData: ITaskData,
    options: ITaskOperationOptions,
    logger: IEventLogger
  ): Promise<ITaskOperationResult> {
    const { correlationId, scenario, applicationFormId } = options;

    try {
      // Validate task data
      this.validateTaskData(taskData);

      // Log operation initiated
      await logger.log(
        { applicationFormId, taskSubject: taskData.Subject },
        `GUS SF create single task initiated for application: ${applicationFormId}`,
        LOGGER_EVENTS.CREATE_TASK_INITIATED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE
      );

      // Get brand-specific API key from global config context
      const apiKey = globalConfigContext.getApiKey();

      // Construct API endpoint
      const endpoint = `salesforce/gus/task`;

      // Make API call
      const taskResult = await postData(
        endpoint,
        taskData,
        correlationId,
        apiKey
      );

      // Validate response
      if (!taskResult?.success) {
        throw new Error(taskResult?.message || "Task creation failed");
      }

      // Log operation completed
      await logger.log(
        {
          applicationFormId,
          taskId: taskResult.data?.id,
          taskSubject: taskData.Subject,
        },
        `GUS SF create single task completed for application: ${applicationFormId}`,
        LOGGER_EVENTS.CREATE_TASK_COMPLETED,
        LOGGER_COMPONENTS.GUS_SALESFORCE
      );

      return {
        success: true,
        data: taskResult.data,
        correlationId,
        tasksProcessed: 1,
      };
    } catch (error) {
      // Log operation failed
      await logger.error(
        { applicationFormId, taskSubject: taskData.Subject },
        `GUS SF create single task failed for application: ${applicationFormId} - ${error.message}`,
        LOGGER_EVENTS.CREATE_TASK_FAILED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE
      );

      return {
        success: false,
        error: error.message || "Unknown error occurred",
        correlationId,
        tasksProcessed: 0,
      };
    }
  }

  /**
   * Creates multiple tasks in Salesforce using bulk API
   *
   * @param tasksData - Array of task data to create
   * @param options - Operation options including correlation ID, scenario, brand
   * @param logger - Event logger for logging operations
   * @returns Promise containing the bulk task creation result
   */
  async createBulkTasks(
    tasksData: ITaskData[],
    options: ITaskOperationOptions,
    logger: IEventLogger
  ): Promise<ITaskOperationResult> {
    const { correlationId, scenario, applicationFormId } = options;

    try {
      // Validate tasks data
      if (!tasksData || tasksData.length === 0) {
        throw new Error("No tasks data provided for bulk creation");
      }

      // Validate each task
      tasksData.forEach((taskData, index) => {
        try {
          this.validateTaskData(taskData);
        } catch (error) {
          throw new Error(
            `Task ${index + 1} validation failed: ${error.message}`
          );
        }
      });

      // Log operation initiated
      await logger.log(
        { applicationFormId, taskCount: tasksData.length },
        `GUS SF create bulk tasks initiated for application: ${applicationFormId} (${tasksData.length} tasks)`,
        LOGGER_EVENTS.CREATE_BULK_TASKS_INITIATED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE
      );

      // Get brand-specific API key from global config context
      const apiKey = globalConfigContext.getApiKey();

      // Construct API endpoint
      const endpoint = `salesforce/gus/tasks/bulk`;

      // Prepare bulk payload
      const bulkPayload = {
        tasks: tasksData,
      };

      // Make API call
      const bulkResult = await postData(
        endpoint,
        bulkPayload,
        correlationId,
        apiKey
      );

      // Validate response
      if (!bulkResult?.success) {
        throw new Error(bulkResult?.message || "Bulk task creation failed");
      }

      // Count successful tasks
      const successfulTasks =
        bulkResult.data?.compositeResponse?.filter(
          (response: any) =>
            response.httpStatusCode >= 200 && response.httpStatusCode < 300
        ).length || 0;

      // Log operation completed
      await logger.log(
        {
          applicationFormId,
          taskCount: tasksData.length,
          successfulTasks,
        },
        `GUS SF create bulk tasks completed for application: ${applicationFormId} (${successfulTasks}/${tasksData.length} successful)`,
        LOGGER_EVENTS.CREATE_BULK_TASKS_COMPLETED,
        LOGGER_COMPONENTS.GUS_SALESFORCE
      );

      return {
        success: true,
        data: bulkResult.data,
        correlationId,
        tasksProcessed: successfulTasks,
      };
    } catch (error) {
      // Log operation failed
      await logger.error(
        { applicationFormId, taskCount: tasksData.length },
        `GUS SF create bulk tasks failed for application: ${applicationFormId} - ${error.message}`,
        LOGGER_EVENTS.CREATE_BULK_TASKS_FAILED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE
      );

      return {
        success: false,
        error: error.message || "Unknown error occurred",
        correlationId,
        tasksProcessed: 0,
      };
    }
  }

  /**
   * Intelligently creates tasks - uses single or bulk API based on task count
   *
   * @param tasksData - Array of task data to create (can be single item)
   * @param options - Operation options
   * @param logger - Event logger for logging operations
   * @returns Promise containing the task creation result
   */
  async createTasks(
    tasksData: ITaskData[],
    options: ITaskOperationOptions,
    logger: IEventLogger
  ): Promise<ITaskOperationResult> {
    if (!tasksData || tasksData.length === 0) {
      throw new Error("No tasks data provided");
    }

    // Use single task API for single task, bulk API for multiple tasks
    if (tasksData.length === GusTaskService.SINGLE_TASK_THRESHOLD) {
      return this.createSingleTask(tasksData[0], options, logger);
    } else {
      return this.createBulkTasks(tasksData, options, logger);
    }
  }

  /**
   * Creates a task with standard default values
   *
   * @param subject - Task subject (defaults to "Review center comments")
   * @param description - Task description
   * @param opportunityId - Related opportunity ID (WhatId)
   * @param status - Task status (defaults to "Open")
   * @param priority - Task priority (defaults to "High")
   * @returns Task data object
   */
  createTaskData(
    subject: string = GusTaskService.DEFAULT_TASK_SUBJECT,
    description: string,
    opportunityId: string,
    status: string = GusTaskService.DEFAULT_TASK_STATUS,
    priority: string = GusTaskService.DEFAULT_TASK_PRIORITY
  ): ITaskData {
    return {
      Subject: subject,
      Description: description,
      WhatId: opportunityId,
      Status: status,
      Priority: priority,
    };
  }

  /**
   * Validates task data structure and required fields
   *
   * @param taskData - Task data to validate
   * @throws Error if validation fails
   */
  private validateTaskData(taskData: ITaskData): void {
    if (!taskData) {
      throw new Error("Task data is required");
    }

    const requiredFields = ["Subject", "Description", "WhatId"];
    const missingFields = requiredFields.filter(
      (field) => !taskData[field as keyof ITaskData]
    );

    if (missingFields.length > 0) {
      throw new Error(
        `Missing required task fields: ${missingFields.join(", ")}`
      );
    }

    // Validate field lengths (Salesforce limits)
    if (taskData.Subject.length > 255) {
      throw new Error("Task subject cannot exceed 255 characters");
    }

    if (taskData.Description.length > 32000) {
      throw new Error("Task description cannot exceed 32000 characters");
    }
  }
}

/**
 * Singleton instance of the GUS Task service
 */
export const gusTaskService = new GusTaskService();
