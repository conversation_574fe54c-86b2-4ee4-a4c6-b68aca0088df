/**
 * @fileoverview Additional Documents Required Validation Schema
 *
 * This module defines the validation schema for additional documents required events.
 * It specifies all required fields, their types, and validation rules
 * for the ADDITIONAL_DOCUMENTS_REQUIRED scenario.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { IScenarioValidationSchema } from "../core/scenarioValidator";
import {
  DOCUMENT_TYPE_VALUES,
  isValidDocumentType,
} from "../constants/documentTypes";

/**
 * Validation schema for additional documents required events
 * Based on the requirements for requesting additional documents from applicants
 */
export const AdditionalDocumentsRequiredValidationSchema: IScenarioValidationSchema =
  {
    scenario: "ADDITIONAL_DOCUMENTS_REQUIRED",
    description: "Validation schema for additional documents required events",
    rules: [
      {
        field: "payload.requiredDocumentTypes",
        type: "custom",
        required: true,
        description:
          "Array of required document types that need to be provided",
        customValidator: (value: any[]) => {
          if (!Array.isArray(value)) {
            return {
              field: "payload.requiredDocumentTypes",
              message: "Required document types must be an array",
            };
          }

          if (value.length === 0) {
            return {
              field: "payload.requiredDocumentTypes",
              message: "Required document types array cannot be empty",
            };
          }

          // Validate each document type in the array
          for (let i = 0; i < value.length; i++) {
            const docType = value[i];
            if (typeof docType !== "string" || docType.trim().length === 0) {
              return {
                field: `payload.requiredDocumentTypes[${i}]`,
                message: "Each document type must be a non-empty string",
              };
            }

            // Validate that the document type is from the allowed list
            if (!isValidDocumentType(docType)) {
              return {
                field: `payload.requiredDocumentTypes[${i}]`,
                message: `Invalid document type '${docType}'. Must be one of: ${DOCUMENT_TYPE_VALUES.join(
                  ", "
                )}`,
              };
            }
          }
          return null;
        },
      },
    ],
  };
