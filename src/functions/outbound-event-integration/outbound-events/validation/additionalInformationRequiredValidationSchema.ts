/**
 * @fileoverview Additional Information Required Validation Schema
 *
 * This module defines the validation schema for additional information required events.
 * It specifies all required fields, their types, and validation rules
 * for the ADDITIONAL_INFORMATION_REQUIRED scenario.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { IScenarioValidationSchema } from "../core/scenarioValidator";

/**
 * Validation schema for additional information required events
 * Based on the requirements for requesting additional information from applicants
 */
export const AdditionalInformationRequiredValidationSchema: IScenarioValidationSchema =
  {
    scenario: "ADDITIONAL_INFORMATION_REQUIRED",
    description: "Validation schema for additional information required events",
    rules: [
      {
        field: "payload.requiredInformation",
        type: "string",
        required: true,
        description: "Required information details that need to be provided",
        minLength: 1,
        customValidator: (value: string) => {
          if (!value || value.trim().length === 0) {
            return {
              field: "payload.requiredInformation",
              message: "Required information cannot be empty",
            };
          }
          return null;
        },
      },
    ],
  };
