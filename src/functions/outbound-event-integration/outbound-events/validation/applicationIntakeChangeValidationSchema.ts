/**
 * @fileoverview Application Intake Change Validation Schema
 *
 * This module defines the validation schema for application intake change events.
 * It specifies all required fields, their types, and validation rules
 * for the APPLICATION_INTAKE_CHANGE scenario.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { IScenarioValidationSchema } from "../core/scenarioValidator";

/**
 * Validation schema for application intake change events
 * Based on the requirements for application intake date modifications
 */
export const ApplicationIntakeChangeValidationSchema: IScenarioValidationSchema =
  {
    scenario: "APPLICATION_INTAKE_CHANGE",
    description: "Validation schema for application intake change events",
    rules: [
      {
        field: "payload.newIntakeDate",
        type: "string",
        required: true,
        description:
          "New intake date for the application (cannot be empty string)",
        customValidator: (value: string) => {
          // Allow empty string or valid date format
          if (value === "") {
            return {
              field: "payload.newIntakeDate",
              message: "New intake date cannot be empty string",
            };
          }

          // Check if it's a valid date format (YYYY-MM-DD or ISO format)
          const datePattern =
            /^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?)?$/;
          if (!datePattern.test(value)) {
            return {
              field: "payload.newIntakeDate",
              message:
                "New intake date must be a valid date format (YYYY-MM-DD or ISO)",
            };
          }

          // Additional validation: check if it's a valid date
          if (isNaN(Date.parse(value))) {
            return {
              field: "payload.newIntakeDate",
              message: "New intake date must be a valid date",
            };
          }

          return null;
        },
      },
      {
        field: "payload.comment",
        type: "string",
        required: false,
        description: "Optional comment about the intake change",
      },
    ],
  };
