import {
  MappingMetadata,
  convertToAlpha3CountryCode,
  createAddress,
} from "./unfcMappings";
import { parsePhoneNumberFromString } from "libphonenumber-js";
import { nanoid } from "nanoid";

/**
 * Metadata for mapping Salesforce data to UNFC person format
 */
export const personMappingMetadata: MappingMetadata = {
  // Basic person information
  id: {
    default: "********-0000-0000-0000-********0000",
  },

  // Name information
  "names[0].firstName": "Account.FirstName",
  "names[0].lastName": "Account.LastName",
  "names[0].middleName": "Account.Middle_Name__c",
  "names[0].fullName": {
    transform: (_, sourceData) => {
      const firstName = sourceData?.Account?.FirstName || "";
      const middleName = sourceData?.Account?.Middle_Name__c || "";
      const lastName = sourceData?.Account?.LastName || "";

      if (middleName) {
        return `${firstName} ${middleName} ${lastName}`.trim();
      } else {
        return `${firstName} ${lastName}`.trim();
      }
    },
  },
  "names[0].type.category": {
    default: "legal",
  },

  // Date of birth
  dateOfBirth: "Account.DateofBirth__c",

  // Language
  personPrimaryLanguage: {
    transform: (_, sourceData, unfcPicklistDetails) => {
      if (!sourceData?.Account?.Primary_Language__c) return undefined;

      const languageCode = sourceData.Account.Primary_Language__c;
      const picklistValues = unfcPicklistDetails["personPrimaryLanguage"];

      if (picklistValues && picklistValues[languageCode]) {
        return picklistValues[languageCode];
      }

      return undefined;
    },
  },

  // Preferred name
  preferredName: "Account.PreferedFirstName__c",

  // Gender information
  gender: "Account.Gender__c",
  "genderIdentity.id": {
    transform: async (_, sourceData, unfcPicklistDetails) => {
      if (!sourceData?.Account?.Gender__c) return undefined;

      const genderIdentityMapping = unfcPicklistDetails["genderIdentity"];

      return genderIdentityMapping[sourceData.Account.Gender__c];
    },
  },

  // Immigration status
  immigrationStatus: {
    transform: async (_, sourceData, unfcPicklistDetails) => {
      if (!sourceData?.Opportunity?.VisaType__c) return undefined;

      const immigrationStatusMapping = unfcPicklistDetails["immigrationStatus"];

      return immigrationStatusMapping[sourceData?.Opportunity?.VisaType__c];
    },
  },

  // Citizenship
  citizenshipCountry: {
    sourcePath: "Account.Citizenship__c",
    transform: (value) =>
      value ? convertToAlpha3CountryCode(value) : undefined,
  },

  // Identity documents
  "identityDocuments[0].documentId":
    "IdentityInfoRecord__c.Identity_Document_Number__c",
  "identityDocuments[0].expiresOn": "IdentityInfoRecord__c.Expiry_Date__c",
  "identityDocuments[0].type.category": {
    default: "passport",
  },
  "identityDocuments[0].country.code": {
    sourcePath: "IdentityInfoRecord__c.Issuing_Country__c",
    transform: (value) =>
      value ? convertToAlpha3CountryCode(value) : undefined,
  },

  // Email information
  emails: {
    transform: (_, sourceData) => {
      const emails = [];

      // Add personal email if available
      if (sourceData?.Account?.PersonEmail) {
        emails.push({
          address: sourceData.Account.PersonEmail,
          type: {
            emailType: "personal",
          },
        });
      }

      return emails.length > 0 ? emails : undefined;
    },
  },

  // Phone information
  phones: {
    transform: (_, sourceData) => {
      const phones = [];

      // Add mobile phone if available
      if (sourceData?.Account?.PersonMobilePhone) {
        try {
          const parsedNumber = parsePhoneNumberFromString(
            sourceData.Account.PersonMobilePhone
          );
          if (parsedNumber) {
            phones.push({
              number: parsedNumber.nationalNumber,
              countryCallingCode: `${parsedNumber.countryCallingCode}`,
              type: {
                phoneType: "other",
              },
            });
          }
        } catch (error) {
          console.warn(`Error parsing phone number: ${error.message}`);
        }
      }

      // Add secondary mobile phone if available
      if (sourceData?.Account?.Mobile__c) {
        try {
          const parsedNumber = parsePhoneNumberFromString(
            sourceData.Account.Mobile__c
          );
          if (parsedNumber) {
            phones.push({
              number: parsedNumber.nationalNumber,
              countryCallingCode: `${parsedNumber.countryCallingCode}`,
              type: {
                phoneType: "other",
              },
            });
          }
        } catch (error) {
          console.warn(`Error parsing phone number: ${error.message}`);
        }
      }

      return phones.length > 0 ? phones : undefined;
    },
  },

  // Address information
  addresses: {
    transform: (_, sourceData, unfcPicklistDetails) => {
      const addresses = [];

      // Determine if the person is domestic (Canadian) based on Citizenship_Status__c
      const citizenshipStatus =
        sourceData?.Account?.Citizenship_Status__c || "";
      const isDomestic = citizenshipStatus.toLowerCase().includes("domestic");

      const addressTypeMapping = unfcPicklistDetails["addressType"];

      const isPermanentAddressDiffer = !!sourceData?.Account?.ShippingStreet;

      const provinceMapping = unfcPicklistDetails["province"];

      // Add mailing address if available
      if (sourceData?.Account?.PersonMailingStreet) {
        const addressLines = isDomestic
          ? [sourceData.Account.PersonMailingStreet]
          : [
              sourceData.Account.PersonMailingStreet,
              sourceData.Account.gaconnector_City__c || "",
              provinceMapping[sourceData.Opportunity.Province__c] || "",
            ].filter((line) => line);

        const mailingAddress = createAddress(
          addressLines,
          sourceData.Account.PersonMailingCity || "",
          isDomestic
            ? provinceMapping[sourceData.Opportunity.Province__c]
            : null,
          convertToAlpha3CountryCode(sourceData.Account.PersonMailingCountry) ||
            "",
          sourceData.Account.PersonMailingPostalCode || "",
          { addressType: "other", detail: { id: addressTypeMapping["other"] } },
          isPermanentAddressDiffer ? false : true
        );

        addresses.push(mailingAddress);
      }

      // Add shipping/other address if available
      if (sourceData?.Account?.ShippingStreet) {
        const addressLines = isDomestic
          ? [sourceData.Account.ShippingStreet]
          : [
              sourceData.Account.ShippingStreet,
              sourceData.Account.ShippingCity || "",
              provinceMapping[sourceData.Opportunity.Permanent_Province__c] ||
                "",
            ].filter((line) => line);

        const otherAddress = createAddress(
          addressLines,
          sourceData.Account.ShippingCity || "",
          isDomestic
            ? provinceMapping[sourceData.Opportunity.Permanent_Province__c]
            : null,
          convertToAlpha3CountryCode(sourceData.Account.ShippingCountry) || "",
          sourceData.Account.ShippingPostalCode || "",
          { addressType: "other", detail: { id: addressTypeMapping["other"] } },
          isPermanentAddressDiffer
        );

        addresses.push(otherAddress);
      }

      return addresses.length > 0 ? addresses : undefined;
    },
  },

  // Alternative credentials
  alternativeCredentials: {
    transform: async (_, sourceData, unfcPicklistDetails) => {
      const credentials = [];

      // Add application form ID credential if available
      if (sourceData.Opportunity.ApplicationId__c) {
        const gusIdType = unfcPicklistDetails["GUSID_TYPE"];
        credentials.push({
          type: gusIdType,
          value: sourceData.Opportunity.ApplicationId__c,
        });
      }

      // Add TRF credential if FirstName is available
      if (
        sourceData.LanguageProficiencyRecord__c &&
        sourceData.LanguageProficiencyRecord__c[0].Test_link_Certification__c
      ) {
        const pickListValue = unfcPicklistDetails["TRF_TYPE"];
        credentials.push({
          type: pickListValue,
          value:
            sourceData.LanguageProficiencyRecord__c[0]
              .Test_link_Certification__c,
        });
      }

      return credentials.length > 0 ? credentials : undefined;
    },
  },
};

/**
 * Metadata for mapping Salesforce data to UNFC application format
 */
export const admissionApplicationMappingMetadata: MappingMetadata = {
  // Basic application information
  id: {
    default: "********-0000-0000-0000-********0000",
  },
  "applicant.id": "PersonId",
  "applicationAcademicPrograms[0].program.id": {
    transform: async (_, sourceData, unfcPicklistDetails) => {
      if (!sourceData?.Opportunity?.Programme__c) return undefined;

      // Define the program ID mappings
      const programMappings =
        unfcPicklistDetails["applicationAcademicPrograms"];

      // Get the program ID from Salesforce
      const programId = sourceData.Opportunity.Programme__c;

      // Check if the program is online
      const isOnline = sourceData.Opportunity.Location__c === "Online";

      // Create the lookup key - add _ONLINE suffix if it's an online program
      let lookupKey = programId;
      if (isOnline && programMappings[`${programId}_ONLINE`]) {
        lookupKey = `${programId}_ONLINE`;
      }

      // Get the mapped program ID
      const mappedProgramId = programMappings[lookupKey];

      // If we have a direct mapping, use it
      if (mappedProgramId) {
        return mappedProgramId;
      }

      // Fallback to picklist mapping if direct mapping not found
      const programMapping = unfcPicklistDetails["applicationAcademicPrograms"];

      return programMapping[programId];
    },
  },
  "site.id": {
    transform: async (_, sourceData, unfcPicklistDetails) => {
      if (!sourceData?.Opportunity?.Location__c) return undefined;

      const siteMapping = unfcPicklistDetails["site"];

      return siteMapping[sourceData.Opportunity.Location__c];
    },
  },
  "academicPeriod.id": {
    transform: async (_, sourceData, unfcPicklistDetails) => {
      if (!sourceData?.Opportunity?.Product_Intake_Date__c) return undefined;

      const intakeDate = new Date(
        sourceData.Opportunity.Product_Intake_Date__c
      );
      const month = intakeDate.getUTCMonth() + 1; // getUTCMonth is 0-indexed

      // Define season mapping (adjust based on your institution's definitions)
      let season: string;
      if ([1, 2, 3].includes(month)) {
        season = "WT";
      } else if ([4, 5, 6].includes(month)) {
        season = "SP";
      } else if ([7, 8, 9].includes(month)) {
        season = "SU";
      } else if ([10, 11, 12].includes(month)) {
        season = "FA"; // Or "Autumn" if consistent naming is preferred
      }

      const academicPeriodMapping = unfcPicklistDetails["academicPeriod"];

      return academicPeriodMapping[
        `${
          sourceData.Opportunity.Product_Intake_Date__c?.split("-")[0]
        }${season}`
      ];
    },
  },
  "residencyType.id": {
    transform: async (_, sourceData, unfcPicklistDetails) => {
      if (!sourceData?.Account?.Citizenship_Status__c) return undefined;

      const immigrationStatusMapping = unfcPicklistDetails["residencyType"];

      return immigrationStatusMapping[sourceData.Account.Citizenship_Status__c];
    },
  },
  academicLoad: {
    default: "fullTime",
  },
  applStatus: {
    default: "AP",
  },
  applStudentType: {
    transform: async (_, sourceData, unfcPicklistDetails) => {
      if (
        !sourceData?.Account?.Citizenship_Status__c ||
        !sourceData?.Opportunity?.LevelCode__c
      )
        return undefined;

      const studentTypeMapping = unfcPicklistDetails["applStudentType"];

      const immigrationStatusKey =
        sourceData.Account.Citizenship_Status__c?.toLowerCase().replaceAll(
          " ",
          "_"
        );
      const levelKey = sourceData.Opportunity.LevelCode__c?.toLowerCase();

      return studentTypeMapping[immigrationStatusKey + "_" + levelKey];
    },
  },
  "admissionPopulation.id": {
    transform: async (_, sourceData, unfcPicklistDetails) => {
      if (!sourceData.Opportunity?.LevelCode__c?.toLowerCase())
        return undefined;

      const admissionPopulationMapping =
        unfcPicklistDetails["admissionPopulation"];

      return admissionPopulationMapping[
        sourceData.Opportunity.LevelCode__c?.toLowerCase()
      ];
    },
  },
};
/**
 * Metadata for mapping Salesforce data to UNFC emergency contact format
 */
export const emergencyContactMappingMetadata: MappingMetadata = {
  id: {
    default: "********-0000-0000-0000-********0000",
  },
  "person.id": "PersonId",
  "contact.name.fullName": {
    transform: (_, sourceData) => {
      const firstName = sourceData?.Connection__c?.First_Name_c__c || "";
      const lastName = sourceData?.Connection__c?.Last_Name__c || "";
      return `${firstName} ${lastName}`.trim();
    },
  },
  "contact.name.firstName": "Connection__c.First_Name_c__c",
  "contact.name.lastName": "Connection__c.Last_Name__c",
  "contact.types[0].id": {
    default: "1def49f9-0f6d-44e8-b978-27280272188b",
  },
  "contact.phones[0].countryCallingCode": {
    transform: (_, sourceData) => {
      const phone = sourceData?.Connection__c?.Phone_c__c;
      if (!phone) return undefined;
      try {
        const parsedNumber = parsePhoneNumberFromString(phone);
        return parsedNumber ? `${parsedNumber.countryCallingCode}` : undefined;
      } catch (error) {
        console.warn(`Error parsing phone number: ${error.message}`);
        return undefined;
      }
    },
  },
  "contact.phones[0].number": {
    transform: (_, sourceData) => {
      const phone = sourceData?.Connection__c?.Phone_c__c;
      if (!phone) return undefined;
      try {
        const parsedNumber = parsePhoneNumberFromString(phone);
        return parsedNumber ? parsedNumber.nationalNumber : phone;
      } catch (error) {
        console.warn(`Error parsing phone number: ${error.message}`);
        return phone;
      }
    },
  },
  "contact.relationship.type": "Connection__c.Relationship__c",
};

/**
 * Metadata for mapping Salesforce data to UNFC visa application format
 */
export const visaApplicationMappingMetadata: MappingMetadata = {
  id: {
    default: "********-0000-0000-0000-********0000",
  },
  "person.id": "PersonId",
  "visaType.category": { default: "nonImmigrant" },
  visaId: "Visa_Application__c.Visa_Number__c",
  issuedOn: "Opportunity.VisaIssueDate__c",
  expiresOn: "Opportunity.VisaExpiryDate__c",
};

/**
 * Metadata for mapping Salesforce data to UNFC academic programs format
 */
//Will throw duplicate check automatically
export const academicProgramsMappingMetadata: MappingMetadata = {
  id: {
    default: "********-0000-0000-0000-********0000",
  },
  "student.id": "PersonId",
  "program.id": {
    transform: async (_, sourceData, unfcPicklistDetails) => {
      if (!sourceData?.Opportunity?.Programme__c) return undefined;

      // Get program mappings from picklist
      const programMapping = unfcPicklistDetails["applicationAcademicPrograms"];

      // Get the program ID from Salesforce
      const programId = sourceData.Opportunity.Programme__c;

      // Check if the program is online
      const isOnline = sourceData.Opportunity.Location__c === "Online";

      // Create the lookup key - add _ONLINE suffix if it's an online program
      let lookupKey = programId;
      if (isOnline && programMapping[`${programId}_ONLINE`]) {
        lookupKey = `${programId}_ONLINE`;
      }

      return programMapping[lookupKey];
    },
  },
  "academicLevel.id": {
    transform: async (_, sourceData, unfcPicklistDetails) => {
      if (!sourceData?.Opportunity?.LevelCode__c) return undefined;

      const academicLevelMapping = unfcPicklistDetails["academicLevel"];

      return academicLevelMapping[sourceData.Opportunity.LevelCode__c];
    },
  },
  "site.id": {
    transform: async (_, sourceData, unfcPicklistDetails) => {
      if (!sourceData?.Opportunity?.Location__c) return undefined;

      const siteMapping = unfcPicklistDetails["site"];

      return siteMapping[sourceData.Opportunity.Location__c];
    },
  },
  curriculumObjective: {
    default: "matriculated",
  },
  "enrollmentStatus.status": {
    default: "active",
  },
  startOn: "Opportunity.Product_Intake_Date__c",
};

/**
 * Metadata for mapping Salesforce data to UNFC aptitude assessment format
 */
export const aptitudeAssessmentMappingMetadata: MappingMetadata = {
  id: {
    default: "********-0000-0000-0000-********0000",
  },
  "student.id": "PersonId",
  "assessment.id": {
    transform: async (_, sourceData, unfcPicklistDetails) => {
      if (
        !sourceData?.LanguageProficiencyRecord__c &&
        !sourceData?.LanguageProficiencyRecord__c?.TestProvider__c
      )
        return undefined;

      const assessmentMapping = unfcPicklistDetails["assessment"];

      return assessmentMapping[
        sourceData?.LanguageProficiencyRecord__c?.TestProvider__c
      ];
    },
  },
  assessedOn: "LanguageProficiencyRecord__c.TestDate__c",
  "score.type": {
    default: "numeric",
  },
  "score.value": {
    transform: (_, sourceData) => {
      if (!sourceData?.LanguageProficiencyRecord__c?.TestScore__c)
        return undefined;
      return Number(sourceData?.LanguageProficiencyRecord__c?.TestScore__c);
    },
  },
  "source.id": {
    default: "40c71eb0-e7b1-49ea-a558-9d5e06690470",
  },
  reported: {
    default: "unofficial",
  },
  "modules[0]": {
    transform: (_, sourceData) => {
      if (!sourceData?.LanguageProficiencyRecord__c?.Listening_Score__c)
        return undefined;
      return {
        moduleName: "LISTENING",
        moduleScore: Number(
          sourceData?.LanguageProficiencyRecord__c?.Listening_Score__c
        ),
      };
    },
  },
  "modules[1]": {
    transform: (_, sourceData) => {
      if (!sourceData?.LanguageProficiencyRecord__c?.Reading_Score__c)
        return undefined;
      return {
        moduleName: "READING",
        moduleScore: Number(
          sourceData?.LanguageProficiencyRecord__c?.Reading_Score__c
        ),
      };
    },
  },
  "modules[2]": {
    transform: (_, sourceData) => {
      if (!sourceData?.LanguageProficiencyRecord__c?.Writing_Score__c)
        return undefined;
      return {
        moduleName: "WRITING",
        moduleScore: Number(
          sourceData?.LanguageProficiencyRecord__c?.Writing_Score__c
        ),
      };
    },
  },
  "modules[3]": {
    transform: (_, sourceData) => {
      if (!sourceData?.LanguageProficiencyRecord__c?.Speaking_Score__c)
        return undefined;
      return {
        moduleName: "SPEAKING",
        moduleScore: Number(
          sourceData?.LanguageProficiencyRecord__c?.Speaking_Score__c
        ),
      };
    },
  },
  comments: "LanguageProficiencyRecord__c.Comments__c",
};

export const externalEducationMappingMetadata: MappingMetadata = {
  id: {
    default: "********-0000-0000-0000-********0000",
  },
  "person.id": "PersonId",
  "institution.id": {
    transform: async (_, sourceData, unfcPicklistDetails) => {
      const institutionMapping =
        unfcPicklistDetails["externalEducationInstitution"];

      return institutionMapping[
        `institution_${sourceData.EducationHistoryRecord__c.orderNumber}`
      ];
    },
  },

  // Attendance periods mapping
  "attendancePeriods[0].startOn": {
    transform: (_, sourceData) => {
      if (!sourceData?.EducationHistoryRecord__c?.EnrolmentDateYear__c)
        return undefined;

      const startDate =
        sourceData.EducationHistoryRecord__c.EnrolmentDateYear__c;

      return {
        year: startDate?.split("-")[0],
        month: startDate.split("-")[1],
        day: startDate.split("-")[2],
      };
    },
  },
  "attendancePeriods[0].endOn": {
    transform: (_, sourceData) => {
      if (!sourceData?.EducationHistoryRecord__c?.GraduationDate__c)
        return undefined;

      const endDate = sourceData.EducationHistoryRecord__c.GraduationDate__c;

      return {
        year: endDate.split("-")[0],
        month: endDate.split("-")[1],
        day: endDate.split("-")[2],
      };
    },
  },

  // Transcript information mapping
  "transcripts[0].type.id": {
    transform: (_, sourceData, unfcPicklistDetails) => {
      if (!sourceData?.EducationHistoryRecord__c?.Education_Level__c)
        return undefined;

      const educationLevel =
        sourceData.EducationHistoryRecord__c.Education_Level__c;
      const sourceMapping = unfcPicklistDetails["educationLevel"];

      return sourceMapping[educationLevel];
    },
  },
  "transcripts[0].status": {
    transform: async (_, __, unfcPicklistDetails) => {
      const transcriptStatusMapping = unfcPicklistDetails["transcriptStatus"];
      return transcriptStatusMapping;
    },
  },

  // Credential mapping
  "credential.id": {
    transform: async (_, sourceData, unfcPicklistDetails) => {
      if (!sourceData?.Opportunity?.TransferCredits__c) return undefined;

      const credentialMapping = unfcPicklistDetails["credential"] || {};
      return credentialMapping[sourceData.Opportunity.TransferCredits__c];
    },
  },

  // Expulsion status mapping
  isExpelled: {
    transform: (_, sourceData) => {
      if (!sourceData?.Opportunity?.EducationHistory_Expelled__c)
        return undefined;

      const isExpelled = sourceData.Opportunity.EducationHistory_Expelled__c;
      if (typeof isExpelled === "boolean") {
        return isExpelled ? "Y" : "N";
      } else if (typeof isExpelled === "string") {
        return isExpelled.toLowerCase() === "yes" ? "Y" : "N";
      }

      return "N";
    },
  },

  comments: {
    transform: (_, sourceData) => {
      if (!sourceData?.Opporunity?.EducationHistoryComments__c)
        return undefined;

      if (sourceData?.Opportunity?.EducationHistoryComments__c) {
        return sourceData.Opportunity.EducationHistoryComments__c;
      }
      return sourceData.Opportunity.EducationHistoryComments__c;
    },
  },
};

export const disabilityMetaData: MappingMetadata = {
  personHealthId: "ColleaguePersonId",
  "disability[0].phlDisability": {
    transform: async (_, sourceData, unfcPicklistDetails) => {
      if (!sourceData?.Opportunity?.Disability__c) return undefined;
      const disability = sourceData.Opportunity.Disability__c;
      const disabilityMapping = unfcPicklistDetails["disability"];
      return disabilityMapping[disability];
    },
  },
};

/**
 * Metadata for mapping Salesforce data to UNFC application remarks format
 */
export const applCoopRemarksMappingMetadata: MappingMetadata = {
  "remarkDetails.type": {
    default: "Includes Co-op WP",
  },
  "remarkDetails.text[0]": {
    default: "Yes",
  },
  "remarkDetails.text[1]": {
    transform: (_, sourceData) => {
      if (!sourceData?.Opportunity?.Coop_WorkPermitNumber__c) return undefined;
      return (
        "Document Number: " + sourceData?.Opportunity?.Coop_WorkPermitNumber__c
      );
    },
  },
  "remarkDetails.text[2]": {
    transform: (_, sourceData) => {
      if (!sourceData?.Opportunity?.Coop_WorkPermitIssueDate__c)
        return undefined;
      return (
        "Issue On: " + sourceData?.Opportunity?.Coop_WorkPermitIssueDate__c
      );
    },
  },
  "remarkDetails.text[3]": {
    transform: (_, sourceData) => {
      if (!sourceData?.Opportunity?.Coop_WorkPermitExpiryDate__c)
        return undefined;
      return (
        "Expiry On: " + sourceData?.Opportunity.Coop_WorkPermitExpiryDate__c
      );
    },
  },
  applicationId: "ApplicationsId",
  "remarkDetails.n60ApplRemarksId": {
    transform: () => {
      return nanoid(8);
    },
  },
};

export const applEducationHistoryRemarksMappingMetadata: MappingMetadata = {
  "remarkDetails.type": {
    default: "Educatn History Completed",
  },
  "remarkDetails.text[0]": "EducationHistoryRecord__c.Study_completed__c",
  applicationId: "ApplicationsId",
  "remarkDetails.n60ApplRemarksId": {
    transform: () => {
      return nanoid(8);
    },
  },
};

export const applicationRemarksMappingMetadata: MappingMetadata = {
  "remarkDetails.type": {
    default: "Salesforce Appn Id",
  },
  "remarkDetails.text[0]": "Opportunity.ApplicationFormId__c",
  applicationId: "ApplicationsId",
  "remarkDetails.n60ApplRemarksId": {
    transform: () => {
      return nanoid(8);
    },
  },
};

export const pathwayProviderRemarksMappingMetadata: MappingMetadata = {
  "remarkDetails.type": {
    default: "Pathway",
  },
  "remarkDetails.text[0]": {
    transform: async (_, sourceData, unfcPicklistDetails) => {
      if (!sourceData?.Opportunity?.Programme__c) return undefined;

      const pathwayProviders = unfcPicklistDetails["pathwayProviders"];

      return pathwayProviders[sourceData.Opportunity.Programme__c];
    },
  },
  applicationId: "ApplicationsId",
  "remarkDetails.n60ApplRemarksId": {
    transform: () => {
      return nanoid(8);
    },
  },
};

export const sftpFileInboundMappingMetadata: MappingMetadata = {
  s3FilePath: "OpportunityFile__c.S3FileName__c",
  ellusionFilePath: {
    transform: (_, sourceData) => {
      if (!sourceData?.OpportunityFile__c?.S3FileName__c) return undefined;
      return (
        "/Inbound/" +
        sourceData?.applicationId +
        "_" +
        sourceData?.colleaguePersonId +
        "/" +
        sourceData?.admissionApplicationId +
        "/" +
        sourceData?.OpportunityFile__c?.DocumentType__c +
        "/" +
        sourceData?.OpportunityFile__c?.Name
      );
    },
  },
  method: { default: "POST_DOC" },
};
