/**
 * Interface for defining required fields for a mapping metadata object
 */
export interface RequiredFieldsDefinition {
  [targetPath: string]: string; // Field path -> Field description
}

/**
 * Type for validation result
 */
export interface ValidationResult {
  isValid: boolean;
  missingFields: string[];
  errors: string[];
}

/**
 * Gets a value from an object by path (e.g., "names[0].firstName")
 * Handles nested paths and array notation
 * @param obj - Source object
 * @param path - Path to the value
 * @returns Value at the path or undefined if not found
 */
export function getValueByPath(obj: Record<string, any>, path: string): any {
  if (!path || !obj) return undefined;

  // Handle array notation like "names[0].firstName"
  const normalizedPath = path.replace(/\[(\d+)\]/g, ".$1");
  const parts = normalizedPath.split(".");

  let current = obj;
  for (const part of parts) {
    if (current == null) {
      return undefined;
    }
    current = current[part];
  }

  return current;
}

/**
 * Validates if required fields exist in the mapped data
 * @param mappedData - Mapped data object (result of transformation)
 * @param requiredFields - Definition of required fields
 * @returns Validation result
 */
export function validateRequiredFields(
  mappedData: Record<string, any>,
  requiredFields: RequiredFieldsDefinition
): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    missingFields: [],
    errors: [],
  };

  // Validate each required field in the mapped data
  for (const [path, description] of Object.entries(requiredFields)) {
    try {
      const value = getValueByPath(mappedData, path);

      // Check if the value exists and is not null/undefined/empty
      if (
        value === undefined ||
        value === null ||
        (typeof value === "string" && value.trim() === "") ||
        (Array.isArray(value) && value.length === 0)
      ) {
        result.isValid = false;
        result.missingFields.push(path);
        result.errors.push(
          `Required field '${description}' (${path}) is missing or empty in the mapped data`
        );
      }
    } catch (error) {
      result.isValid = false;
      result.errors.push(
        `Error validating '${description}' (${path}): ${error.message}`
      );
    }
  }

  return result;
}

/**
 * Required fields for person mapping metadata
 */
export const personRequiredFields: RequiredFieldsDefinition = {
  "names[0].fullName": "Full name",
  id: "Person ID",
};

/**
 * Required fields for admission application mapping metadata
 */
export const admissionApplicationRequiredFields: RequiredFieldsDefinition = {
  "applicant.id": "Applicant ID",
  "applicationAcademicPrograms[0].program.id": "Program ID",
};

/**
 * Required fields for emergency contact mapping metadata
 */
export const emergencyContactRequiredFields: RequiredFieldsDefinition = {
  "person.id": "Person ID",
  "contact.name.fullName": "Contact full name",
  "contact.types[0].id": "Relationship type",
};

/**
 * Required fields for visa application mapping metadata
 */
export const visaApplicationRequiredFields: RequiredFieldsDefinition = {
  "person.id": "Person ID",
  "visaType.category": "Visa category",
};

/**
 * Required fields for academic programs mapping metadata
 */
export const academicProgramsRequiredFields: RequiredFieldsDefinition = {
  "student.id": "Student ID",
  "program.id": "Program ID",
  curriculumObjective: "Curriculum Objective",
  enrollmentStatus: "Enrollment Status",
};

/**
 * Required fields for aptitude assessment mapping metadata
 */
export const aptitudeAssessmentRequiredFields: RequiredFieldsDefinition = {
  "student.id": "Student ID",
  "assessment.id": "Assessment ID",
  assessedOn: "Assessment date",
  "score.value": "Score value",
};

/**
 * Required fields for external education mapping metadata
 */
export const externalEducationRequiredFields: RequiredFieldsDefinition = {
  "person.id": "Student ID",
  "institution.id": "Institution ID",
  id: "External Education ID",
};

/**
 * Required fields for disability mapping metadata
 */
export const disabilityRequiredFields: RequiredFieldsDefinition = {
  personHealthId: "Person Health ID",
  "disability[0].phlDisability": "Disability",
};

/**
 * Required fields for application remarks mapping metadata
 */
export const applicationRemarksRequiredFields: RequiredFieldsDefinition = {
  "remarkDetails.type": "Remark type",
  "remarkDetails.text[0]": "Remark text",
  "remarkDetails.n60ApplRemarksId": "Applications Id",
};

/**
 * Required fields for SFTP file inbound mapping metadata
 */
export const sftpFileInboundRequiredFields: RequiredFieldsDefinition = {
  s3FilePath: "S3 file name",
  ellusionFilePath: "SFTP File path",
  method: "Method",
};

export const pathwayProviderRemarksRequiredFields: RequiredFieldsDefinition = {
  "remarkDetails.type": "Remark type",
  "remarkDetails.text[0]": "Remark text",
  "remarkDetails.n60ApplRemarksId": "Applications Id",
};

/**
 * Map of mapping metadata types to their required fields
 */
export const requiredFieldsMap: Record<string, RequiredFieldsDefinition> = {
  person: personRequiredFields,
  admissionApplication: admissionApplicationRequiredFields,
  emergencyContact: emergencyContactRequiredFields,
  visaApplication: visaApplicationRequiredFields,
  academicPrograms: academicProgramsRequiredFields,
  aptitudeAssessment: aptitudeAssessmentRequiredFields,
  externalEducation: externalEducationRequiredFields,
  disability: disabilityRequiredFields,
  applicationRemarks: applicationRemarksRequiredFields,
  applEducationHistoryRemarks: applicationRemarksRequiredFields,
  applCoopRemarks: applicationRemarksRequiredFields,
  sftpFileInbound: sftpFileInboundRequiredFields,
  pathwayProviderRemarks: pathwayProviderRemarksRequiredFields
};

/**
 * Validates a mapped data object against required fields
 * @param mappedData - Mapped data object (result of transformation)
 * @param mappingType - Type of mapping metadata
 * @returns Validation result
 */
export function validateMapping(
  mappedData: Record<string, any>,
  mappingType: string
): ValidationResult {
  const requiredFields = requiredFieldsMap[mappingType];
  if (!requiredFields) {
    return { isValid: true, missingFields: [], errors: [] };
  }

  return validateRequiredFields(mappedData, requiredFields);
}
